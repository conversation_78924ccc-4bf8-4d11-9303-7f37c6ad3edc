import { Tabs } from "expo-router";
import { AuthProvider } from "../../context/AuthContext";
import { Ionicons } from "@expo/vector-icons";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import MaterialCommunityIcons from "@expo/vector-icons/MaterialCommunityIcons";
import { useFonts, BakbakOne_400Regular } from "@expo-google-fonts/bakbak-one";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useState } from "react";

export default function TabLayout() {
  const [fontsLoaded] = useFonts({
    BakbakOne_400Regular,
  });

  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 60 * 1000, // 1 minute
            gcTime: 5 * 60 * 1000, // 5 minutes (previously cacheTime)
          },
        },
      })
  );

  if (!fontsLoaded) {
    return null;
  }

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <Tabs
          screenOptions={{
            tabBarActiveTintColor: "#D62828", // Red color for active tab
            tabBarInactiveTintColor: "#8E8E93",
            // Global header styling applied to all tabs
            headerTintColor: "#231716", // Text color for all headers
            headerStyle: {
              backgroundColor: "#D62828", // Red header background
            },
            headerTitleStyle: {
              fontSize: 30,
              color: "#fff", // White text on red background
              fontFamily: "BakbakOne_400Regular",
            },
            headerTitleAlign: "left",
            headerStatusBarHeight: 2,
            tabBarStyle: {
              backgroundColor: "#fff",
              borderTopWidth: 1,
              borderTopColor: "#e5e5e7",
            },
            tabBarLabelStyle: {
              fontSize: 12,
            },
          }}
        >
          <Tabs.Screen
            name="tournaments"
            options={{
              title: "Tournaments",
              headerShown: false,
              tabBarIcon: ({ color, size }) => (
                <MaterialIcons
                  name="sports-hockey"
                  size={size + 3}
                  color={color}
                />
              ),
            }}
          />
          <Tabs.Screen
            name="camps"
            options={{
              title: "Camps",
              headerShown: false,
              tabBarIcon: ({ color, size }) => (
                <MaterialCommunityIcons
                  name="campfire"
                  size={size}
                  color={color}
                />
              ),
            }}
          />
          <Tabs.Screen
            name="showcases"
            options={{
              title: "Showcases",
              headerShown: false,
              tabBarIcon: ({ color, size }) => (
                <MaterialCommunityIcons
                  name="account-group"
                  size={size}
                  color={color}
                />
              ),
            }}
          />
          <Tabs.Screen
            name="leagues"
            options={{
              title: "Leagues",
              headerShown: false,
              tabBarIcon: ({ color, size }) => (
                <MaterialCommunityIcons
                  name="trophy-variant"
                  size={size}
                  color={color}
                />
              ),
            }}
          />
          <Tabs.Screen
            name="favourites"
            options={{
              title: "Favourites",
              headerShown: false,
              tabBarIcon: ({ color, size }) => (
                <Ionicons name="heart" size={size} color={color} />
              ),
            }}
          />
          <Tabs.Screen
            name="more"
            options={{
              title: "More",
              headerShown: false,
              tabBarIcon: ({ color, size }) => (
                <Ionicons
                  name="ellipsis-horizontal"
                  size={size}
                  color={color}
                />
              ),
            }}
          />
        </Tabs>
      </AuthProvider>
    </QueryClientProvider>
  );
}
