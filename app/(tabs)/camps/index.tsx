import React, { useState, useEffect } from "react";
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, ActivityIndicator } from "react-native";
import { useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import Header from "./components/Header";
import CampCard from "./components/CampsCard";
import AddCampModal from "./components/AddCampModal";
import {
  getcampsItems,
  campsItem,
  campsDetails,
  deletecampsItem,
} from "../../../data/camps/camps";

export const navigationOptions = {
  headerShown: false,
};

export default function CampsScreen() {
  const [camps, setCamps] = useState<campsItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingCamp, setEditingCamp] = useState<campsDetails | null>(null);
  const router = useRouter();

  useEffect(() => {
    loadCamps();
  }, []);

  const loadCamps = async () => {
    setLoading(true);
    try {
      const data = await getcampsItems();
      setCamps(data);
    } catch (error) {
      Alert.alert("Error", "Failed to load camps.");
    } finally {
      setLoading(false);
    }
  };

  const handleAddPress = () => {
    setEditingCamp(null);
    setShowAddModal(true);
  };

  const handleEditPress = (item: campsDetails) => {
    setEditingCamp(item);
    setShowAddModal(true);
  };

  const handleAddModalClose = () => {
    setShowAddModal(false);
    setEditingCamp(null);
    loadCamps();
  };

  const handleRemoveCamp = async (item: campsItem) => {
    try {
      await deletecampsItem(item.docId);
      setCamps((prev) => prev.filter((camp) => camp.docId !== item.docId));
    } catch (error) {
      Alert.alert("Error", "Failed to delete camp.");
    }
  };

  return (
    <View style={styles.container}>
      <Header
        title="Camps"
        onFilterPress={() => {}}
        onAddPress={handleAddPress}
      />
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#D52B1E" />
        </View>
      ) : (
        <ScrollView style={styles.contentContainer}>
          <View style={styles.content}>
            {camps.map((item) => (
              <View key={item.docId} style={{ position: "relative" }}>
                <TouchableOpacity
                  onPress={() => router.push(`/camps/${item.docId}`)}
                  activeOpacity={0.8}
                >
                  <CampCard item={item} onEdit={() => handleEditPress(item)} />
                  <TouchableOpacity
                    style={styles.deleteButton}
                    onPress={() => {
                      Alert.alert(
                        "Delete Camp",
                        "Are you sure you want to delete this camp?",
                        [
                          { text: "Cancel", style: "cancel" },
                          {
                            text: "Delete",
                            style: "destructive",
                            onPress: () => handleRemoveCamp(item),
                          },
                        ]
                      );
                    }}
                  >
                    <Ionicons name="trash" size={22} color="#fff" />
                  </TouchableOpacity>
                </TouchableOpacity>
              </View>
            ))}
            {camps.length === 0 && (
              <Text style={{ textAlign: "center", marginTop: 32, color: "#888" }}>
                No camps found.
              </Text>
            )}
          </View>
        </ScrollView>
      )}
      <AddCampModal
        visible={showAddModal}
        onClose={handleAddModalClose}
        editingcamps={editingCamp}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  contentContainer: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  content: {
    paddingHorizontal: 10,
    paddingVertical: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  deleteButton: {
    position: "absolute",
    bottom: 10,
    right: 10,
    backgroundColor: "#D52B1E",
    borderRadius: 16,
    padding: 6,
    zIndex: 2,
    elevation: 3,
    marginBottom: 16,
    marginRight: 3,
  },
});
