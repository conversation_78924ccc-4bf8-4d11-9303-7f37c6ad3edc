import React from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Modal,
} from "react-native";
import { Ionicons, MaterialIcons } from "@expo/vector-icons";

interface Coach {
  id: string;
  name: string;
  title: string;
  experience: string;
  specialization: string[];
  bio: string;
  achievements: string[];
  contact: {
    email: string;
    phone: string;
  };
  avatar: string;
  rating: number;
  campsLed: number;
}

interface CoachProfileProps {
  coach: Coach;
  visible: boolean;
  onClose: () => void;
}

const mockCoach: Coach = {
  id: "1",
  name: "<PERSON>",
  title: "Head Coach & Skills Development Specialist",
  experience: "15 years",
  specialization: ["Power Skating", "Puck Handling", "Shooting Techniques", "Game Strategy"],
  bio: "<PERSON> is a former professional hockey player with over 15 years of coaching experience. He has worked with players at all levels, from youth hockey to professional leagues. His innovative training methods have helped hundreds of players improve their skills and reach their potential.",
  achievements: [
    "Former NHL Player - Boston Bruins (5 years)",
    "USA Hockey Level 4 Certification",
    "Coached 3 championship teams",
    "Developed 25+ players who went on to college hockey",
    "Hockey Canada Skills Development Instructor"
  ],
  contact: {
    email: "<EMAIL>",
    phone: "555-0123"
  },
  avatar: "https://picsum.photos/200/200?random=10",
  rating: 4.9,
  campsLed: 47
};

export default function CoachProfile({ coach = mockCoach, visible, onClose }: CoachProfileProps) {
  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <Ionicons key={i} name="star" size={16} color="#FFD700" />
      );
    }

    if (hasHalfStar) {
      stars.push(
        <Ionicons key="half" name="star-half" size={16} color="#FFD700" />
      );
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <Ionicons key={`empty-${i}`} name="star-outline" size={16} color="#FFD700" />
      );
    }

    return stars;
  };

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color="#666" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Coach Profile</Text>
          <TouchableOpacity style={styles.contactButton}>
            <MaterialIcons name="contact-mail" size={24} color="#D52B1E" />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Profile Header */}
          <View style={styles.profileHeader}>
            <Image source={{ uri: coach.avatar }} style={styles.coachAvatar} />
            <View style={styles.profileInfo}>
              <Text style={styles.coachName}>{coach.name}</Text>
              <Text style={styles.coachTitle}>{coach.title}</Text>
              
              <View style={styles.ratingContainer}>
                <View style={styles.starsContainer}>
                  {renderStars(coach.rating)}
                </View>
                <Text style={styles.ratingText}>{coach.rating}</Text>
              </View>
              
              <View style={styles.statsContainer}>
                <View style={styles.statItem}>
                  <Text style={styles.statNumber}>{coach.campsLed}</Text>
                  <Text style={styles.statLabel}>Camps Led</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statNumber}>{coach.experience}</Text>
                  <Text style={styles.statLabel}>Experience</Text>
                </View>
              </View>
            </View>
          </View>

          {/* Contact Information */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Contact Information</Text>
            <View style={styles.contactInfo}>
              <TouchableOpacity style={styles.contactItem}>
                <Ionicons name="mail" size={20} color="#D52B1E" />
                <Text style={styles.contactText}>{coach.contact.email}</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.contactItem}>
                <Ionicons name="call" size={20} color="#D52B1E" />
                <Text style={styles.contactText}>{coach.contact.phone}</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Specialization */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Specialization</Text>
            <View style={styles.specializationContainer}>
              {coach.specialization.map((skill, index) => (
                <View key={index} style={styles.skillBadge}>
                  <Text style={styles.skillText}>{skill}</Text>
                </View>
              ))}
            </View>
          </View>

          {/* Biography */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Biography</Text>
            <Text style={styles.bioText}>{coach.bio}</Text>
          </View>

          {/* Achievements */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Achievements & Certifications</Text>
            <View style={styles.achievementsList}>
              {coach.achievements.map((achievement, index) => (
                <View key={index} style={styles.achievementItem}>
                  <MaterialIcons name="emoji-events" size={20} color="#FFD700" />
                  <Text style={styles.achievementText}>{achievement}</Text>
                </View>
              ))}
            </View>
          </View>

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <TouchableOpacity style={styles.messageButton}>
              <MaterialIcons name="message" size={20} color="#fff" />
              <Text style={styles.messageButtonText}>Send Message</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.scheduleButton}>
              <Ionicons name="calendar" size={20} color="#D52B1E" />
              <Text style={styles.scheduleButtonText}>Schedule Meeting</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#E5E5E7",
  },
  closeButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#1a1a1a",
  },
  contactButton: {
    padding: 8,
  },
  content: {
    flex: 1,
  },
  profileHeader: {
    backgroundColor: "#fff",
    padding: 20,
    alignItems: "center",
    borderBottomWidth: 1,
    borderBottomColor: "#E5E5E7",
  },
  coachAvatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
    marginBottom: 16,
  },
  profileInfo: {
    alignItems: "center",
  },
  coachName: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#1a1a1a",
    marginBottom: 4,
  },
  coachTitle: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    marginBottom: 12,
  },
  ratingContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  starsContainer: {
    flexDirection: "row",
    marginRight: 8,
  },
  ratingText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1a1a1a",
  },
  statsContainer: {
    flexDirection: "row",
    gap: 40,
  },
  statItem: {
    alignItems: "center",
  },
  statNumber: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#D52B1E",
  },
  statLabel: {
    fontSize: 14,
    color: "#666",
    marginTop: 4,
  },
  section: {
    backgroundColor: "#fff",
    margin: 16,
    padding: 16,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#1a1a1a",
    marginBottom: 12,
  },
  contactInfo: {
    gap: 12,
  },
  contactItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 8,
  },
  contactText: {
    fontSize: 16,
    color: "#1a1a1a",
    marginLeft: 12,
  },
  specializationContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  skillBadge: {
    backgroundColor: "#E3F2FD",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: "#2196F3",
  },
  skillText: {
    fontSize: 14,
    color: "#2196F3",
    fontWeight: "600",
  },
  bioText: {
    fontSize: 16,
    color: "#666",
    lineHeight: 24,
  },
  achievementsList: {
    gap: 12,
  },
  achievementItem: {
    flexDirection: "row",
    alignItems: "flex-start",
  },
  achievementText: {
    fontSize: 15,
    color: "#1a1a1a",
    marginLeft: 12,
    flex: 1,
    lineHeight: 20,
  },
  actionButtons: {
    flexDirection: "row",
    gap: 12,
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  messageButton: {
    flex: 1,
    backgroundColor: "#D52B1E",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 14,
    borderRadius: 12,
    gap: 8,
  },
  messageButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  scheduleButton: {
    flex: 1,
    backgroundColor: "#fff",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 14,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: "#D52B1E",
    gap: 8,
  },
  scheduleButtonText: {
    color: "#D52B1E",
    fontSize: 16,
    fontWeight: "600",
  },
});
