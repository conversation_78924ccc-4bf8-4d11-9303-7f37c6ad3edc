import React from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
  ScrollView,
} from "react-native";
import { useRouter } from "expo-router";
import { Ionicons, MaterialIcons } from "@expo/vector-icons";
import { campsItem } from "../../../../data/camps/camps";
import { useFonts, BakbakOne_400Regular } from "@expo-google-fonts/bakbak-one";

const { width } = Dimensions.get("window");

interface CampsCardProps {
  item: campsItem;
  onEdit?: (item: campsItem) => void;
}

export default function CampsCard({ item, onEdit }: CampsCardProps) {
  const router = useRouter();

  const handleEdit = (e: any) => {
    e.stopPropagation();
    onEdit?.(item);
  };

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={() => {
        router.push(`/camps/${item.docId}`);
      }}
      activeOpacity={0.7}
    >
      <View style={styles.mainContent}>
        <Image source={{ uri: item.image }} style={styles.image} />
        <View style={styles.textContent}>
          <View style={styles.titleRow}>
            <Text style={styles.title} numberOfLines={2}>
              {item.title}
            </Text>
            <TouchableOpacity
              style={styles.editButton}
              onPress={handleEdit}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <MaterialIcons name="mode-edit" size={19} color="#fff" />
            </TouchableOpacity>
          </View>
          <Text style={styles.subtitle} numberOfLines={2}>
            {item.subtitle}
          </Text>
          <Text style={styles.date}>{item.date}</Text>
        </View>
      </View>
      <ScrollView
        horizontal
        style={styles.tagsContainer}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.tagsScrollContent}
      >
        {item.tags.map((tag, index) => (
          <View key={index} style={styles.tag}>
            <Text style={styles.tagText}>{tag}</Text>
          </View>
        ))}
      </ScrollView>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#FFFFFF",
    borderRadius: 16,
    marginBottom: 16,
    flexDirection: "column",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 1,
    borderWidth: 0.2,
    borderColor: "#CACDD4",
  },
  mainContent: {
    flexDirection: "row",
    padding: 16,
  },
  image: {
    width: 120,
    height: 90,
    borderRadius: 6,
    backgroundColor: "#8B8B8B",
    marginRight: 16,
  },
  textContent: {
    flex: 1,
    justifyContent: "flex-start",
  },
  titleRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 4,
  },
  title: {
    fontSize: 21,
    color: "#D52B1E",
    marginTop: 2,
    lineHeight: 22,
    fontFamily: "BakbakOne_400Regular",
    flex: 1,
    marginRight: 8,
  },
  editButton: {
    padding: 4,
    borderRadius: 4,
    backgroundColor: "#346eca",
  },
  subtitle: {
    fontSize: 15,
    color: "#231716",
    fontWeight: "700",
    marginBottom: 10,
  },
  date: {
    fontSize: 15,
    color: "#231716",
    fontWeight: "500",
  },
  tagsContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  tagsScrollContent: {
    flexDirection: "row",
    gap: 6,
  },
  tag: {
    backgroundColor: "#231716",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  tagText: {
    fontSize: 12,
    color: "#FFFFFF",
    fontWeight: "500",
  },
});
