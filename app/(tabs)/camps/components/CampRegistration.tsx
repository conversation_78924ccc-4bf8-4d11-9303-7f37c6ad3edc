import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Modal,
  Alert,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface CampRegistrationProps {
  camp: any;
  visible: boolean;
  onClose: () => void;
}

interface RegistrationForm {
  participantName: string;
  age: string;
  email: string;
  phone: string;
  emergencyContact: string;
  emergencyPhone: string;
  medicalInfo: string;
  skillLevel: string;
  previousExperience: string;
}

export default function CampRegistration({ camp, visible, onClose }: CampRegistrationProps) {
  const [formData, setFormData] = useState<RegistrationForm>({
    participantName: "",
    age: "",
    email: "",
    phone: "",
    emergencyContact: "",
    emergencyPhone: "",
    medicalInfo: "",
    skillLevel: "beginner",
    previousExperience: "",
  });

  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 3;

  const updateFormData = (field: keyof RegistrationForm, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = () => {
    Alert.alert(
      "Registration Submitted", 
      "Thank you for registering! You will receive a confirmation email shortly.",
      [{ text: "OK", onPress: onClose }]
    );
  };

  const renderStep1 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Participant Information</Text>
      
      <View style={styles.inputGroup}>
        <Text style={styles.label}>Full Name *</Text>
        <TextInput
          style={styles.input}
          value={formData.participantName}
          onChangeText={(value) => updateFormData("participantName", value)}
          placeholder="Enter participant's full name"
        />
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Age *</Text>
        <TextInput
          style={styles.input}
          value={formData.age}
          onChangeText={(value) => updateFormData("age", value)}
          placeholder="Enter age"
          keyboardType="numeric"
        />
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Email Address *</Text>
        <TextInput
          style={styles.input}
          value={formData.email}
          onChangeText={(value) => updateFormData("email", value)}
          placeholder="Enter email address"
          keyboardType="email-address"
          autoCapitalize="none"
        />
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Phone Number *</Text>
        <TextInput
          style={styles.input}
          value={formData.phone}
          onChangeText={(value) => updateFormData("phone", value)}
          placeholder="Enter phone number"
          keyboardType="phone-pad"
        />
      </View>
    </View>
  );

  const renderStep2 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Emergency Contact & Medical</Text>
      
      <View style={styles.inputGroup}>
        <Text style={styles.label}>Emergency Contact Name *</Text>
        <TextInput
          style={styles.input}
          value={formData.emergencyContact}
          onChangeText={(value) => updateFormData("emergencyContact", value)}
          placeholder="Enter emergency contact name"
        />
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Emergency Contact Phone *</Text>
        <TextInput
          style={styles.input}
          value={formData.emergencyPhone}
          onChangeText={(value) => updateFormData("emergencyPhone", value)}
          placeholder="Enter emergency contact phone"
          keyboardType="phone-pad"
        />
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Medical Information</Text>
        <TextInput
          style={[styles.input, styles.textArea]}
          value={formData.medicalInfo}
          onChangeText={(value) => updateFormData("medicalInfo", value)}
          placeholder="Any medical conditions, allergies, or medications"
          multiline
          numberOfLines={4}
        />
      </View>
    </View>
  );

  const renderStep3 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Hockey Experience</Text>
      
      <View style={styles.inputGroup}>
        <Text style={styles.label}>Skill Level *</Text>
        <View style={styles.skillLevelContainer}>
          {["beginner", "intermediate", "advanced"].map((level) => (
            <TouchableOpacity
              key={level}
              style={[
                styles.skillLevelButton,
                formData.skillLevel === level && styles.skillLevelSelected
              ]}
              onPress={() => updateFormData("skillLevel", level)}
            >
              <Text style={[
                styles.skillLevelText,
                formData.skillLevel === level && styles.skillLevelSelectedText
              ]}>
                {level.charAt(0).toUpperCase() + level.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Previous Hockey Experience</Text>
        <TextInput
          style={[styles.input, styles.textArea]}
          value={formData.previousExperience}
          onChangeText={(value) => updateFormData("previousExperience", value)}
          placeholder="Describe any previous hockey experience, teams played for, etc."
          multiline
          numberOfLines={4}
        />
      </View>

      <View style={styles.summaryCard}>
        <Text style={styles.summaryTitle}>Registration Summary</Text>
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Camp:</Text>
          <Text style={styles.summaryValue}>{camp?.name}</Text>
        </View>
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Dates:</Text>
          <Text style={styles.summaryValue}>{camp?.startDate} - {camp?.endDate}</Text>
        </View>
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Price:</Text>
          <Text style={styles.summaryPrice}>{camp?.price}</Text>
        </View>
      </View>
    </View>
  );

  const renderStepContent = () => {
    switch (currentStep) {
      case 1: return renderStep1();
      case 2: return renderStep2();
      case 3: return renderStep3();
      default: return renderStep1();
    }
  };

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={24} color="#666" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Camp Registration</Text>
          <View style={styles.stepIndicator}>
            <Text style={styles.stepText}>{currentStep}/{totalSteps}</Text>
          </View>
        </View>

        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View 
              style={[
                styles.progressFill, 
                { width: `${(currentStep / totalSteps) * 100}%` }
              ]} 
            />
          </View>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {renderStepContent()}
        </ScrollView>

        <View style={styles.footer}>
          <TouchableOpacity
            style={[styles.footerButton, styles.previousButton]}
            onPress={handlePrevious}
            disabled={currentStep === 1}
          >
            <Text style={[
              styles.footerButtonText,
              currentStep === 1 && styles.disabledButtonText
            ]}>
              Previous
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.footerButton, styles.nextButton]}
            onPress={currentStep === totalSteps ? handleSubmit : handleNext}
          >
            <Text style={styles.nextButtonText}>
              {currentStep === totalSteps ? "Submit Registration" : "Next"}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#E5E5E7",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#1a1a1a",
  },
  stepIndicator: {
    backgroundColor: "#D52B1E",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  stepText: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "600",
  },
  progressContainer: {
    backgroundColor: "#fff",
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  progressBar: {
    height: 4,
    backgroundColor: "#E5E5E7",
    borderRadius: 2,
  },
  progressFill: {
    height: "100%",
    backgroundColor: "#D52B1E",
    borderRadius: 2,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  stepContainer: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#1a1a1a",
    marginBottom: 20,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: "#E5E5E7",
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: "#fff",
  },
  textArea: {
    height: 100,
    textAlignVertical: "top",
  },
  skillLevelContainer: {
    flexDirection: "row",
    gap: 8,
  },
  skillLevelButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#E5E5E7",
    alignItems: "center",
  },
  skillLevelSelected: {
    backgroundColor: "#D52B1E",
    borderColor: "#D52B1E",
  },
  skillLevelText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#666",
  },
  skillLevelSelectedText: {
    color: "#fff",
  },
  summaryCard: {
    backgroundColor: "#f8f9fa",
    borderRadius: 8,
    padding: 16,
    marginTop: 16,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#1a1a1a",
    marginBottom: 12,
  },
  summaryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 14,
    color: "#666",
  },
  summaryValue: {
    fontSize: 14,
    color: "#1a1a1a",
    fontWeight: "500",
  },
  summaryPrice: {
    fontSize: 16,
    color: "#D52B1E",
    fontWeight: "bold",
  },
  footer: {
    flexDirection: "row",
    gap: 12,
    padding: 16,
    backgroundColor: "#fff",
    borderTopWidth: 1,
    borderTopColor: "#E5E5E7",
  },
  footerButton: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: "center",
  },
  previousButton: {
    backgroundColor: "#f0f0f0",
  },
  nextButton: {
    backgroundColor: "#D52B1E",
  },
  footerButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#666",
  },
  nextButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#fff",
  },
  disabledButtonText: {
    color: "#ccc",
  },
});
