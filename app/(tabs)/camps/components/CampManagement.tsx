import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Modal,
  TextInput,
  Alert,
} from "react-native";
import { Ionicons, MaterialIcons } from "@expo/vector-icons";

interface Attendee {
  id: string;
  name: string;
  age: number;
  email: string;
  phone: string;
  emergencyContact: string;
  registrationDate: string;
  paymentStatus: "paid" | "pending" | "refunded";
  avatar?: string;
}

interface CampManagementProps {
  campId: string;
  campName: string;
  visible: boolean;
  onClose: () => void;
  initialTab?: string;
}

const mockAttendees: Attendee[] = [
  {
    id: "1",
    name: "<PERSON>",
    age: 15,
    email: "<EMAIL>",
    phone: "555-0101",
    emergencyContact: "555-0102",
    registrationDate: "2025-07-15",
    paymentStatus: "paid",
  },
  {
    id: "2",
    name: "<PERSON>",
    age: 14,
    email: "<EMAIL>",
    phone: "555-0201",
    emergencyContact: "555-0202",
    registrationDate: "2025-07-16",
    paymentStatus: "pending",
  },
];

export default function CampManagement({ campId, campName, visible, onClose, initialTab = "attendees" }: CampManagementProps) {
  const [activeTab, setActiveTab] = useState(initialTab);
  const [attendees, setAttendees] = useState<Attendee[]>(mockAttendees);
  const [searchQuery, setSearchQuery] = useState("");
  const [showMessageModal, setShowMessageModal] = useState(false);
  const [messageText, setMessageText] = useState("");

  // Reset to initial tab when modal opens
  useEffect(() => {
    if (visible) {
      setActiveTab(initialTab);
    }
  }, [visible, initialTab]);

  const filteredAttendees = attendees.filter(attendee =>
    attendee.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    attendee.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case "paid": return "#4CAF50";
      case "pending": return "#FF9800";
      case "refunded": return "#F44336";
      default: return "#666";
    }
  };

  const sendMessage = () => {
    if (messageText.trim()) {
      Alert.alert("Message Sent", "Your message has been sent to all attendees.");
      setMessageText("");
      setShowMessageModal(false);
    }
  };

  const renderAttendeeCard = (attendee: Attendee) => (
    <View key={attendee.id} style={styles.attendeeCard}>
      <View style={styles.attendeeHeader}>
        <View style={styles.attendeeInfo}>
          <View style={styles.avatarContainer}>
            {attendee.avatar ? (
              <Image source={{ uri: attendee.avatar }} style={styles.avatar} />
            ) : (
              <View style={styles.avatarPlaceholder}>
                <Text style={styles.avatarText}>
                  {attendee.name.charAt(0).toUpperCase()}
                </Text>
              </View>
            )}
          </View>
          <View style={styles.attendeeDetails}>
            <Text style={styles.attendeeName}>{attendee.name}</Text>
            <Text style={styles.attendeeAge}>Age: {attendee.age}</Text>
            <Text style={styles.attendeeEmail}>{attendee.email}</Text>
          </View>
        </View>
        <View style={[
          styles.paymentStatus,
          { backgroundColor: getPaymentStatusColor(attendee.paymentStatus) }
        ]}>
          <Text style={styles.paymentStatusText}>
            {attendee.paymentStatus.toUpperCase()}
          </Text>
        </View>
      </View>
      
      <View style={styles.attendeeActions}>
        <TouchableOpacity style={styles.actionButton}>
          <Ionicons name="call" size={16} color="#666" />
          <Text style={styles.actionButtonText}>Call</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton}>
          <Ionicons name="mail" size={16} color="#666" />
          <Text style={styles.actionButtonText}>Email</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton}>
          <MaterialIcons name="more-vert" size={16} color="#666" />
          <Text style={styles.actionButtonText}>More</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderScheduleTab = () => (
    <ScrollView style={styles.tabContent}>
      <View style={styles.scheduleHeader}>
        <Text style={styles.sectionTitle}>Daily Schedule</Text>
        <TouchableOpacity style={styles.addScheduleButton}>
          <Ionicons name="add" size={20} color="#fff" />
          <Text style={styles.addScheduleText}>Add Session</Text>
        </TouchableOpacity>
      </View>
      
      {[1, 2, 3, 4, 5].map(day => (
        <View key={day} style={styles.dayCard}>
          <Text style={styles.dayTitle}>Day {day}</Text>
          <View style={styles.sessionsList}>
            <View style={styles.session}>
              <Text style={styles.sessionTime}>9:00 AM - 10:30 AM</Text>
              <Text style={styles.sessionTitle}>On-Ice Skills Training</Text>
              <Text style={styles.sessionCoach}>Coach: Mike Johnson</Text>
            </View>
            <View style={styles.session}>
              <Text style={styles.sessionTime}>11:00 AM - 12:00 PM</Text>
              <Text style={styles.sessionTitle}>Strategy & Game Analysis</Text>
              <Text style={styles.sessionCoach}>Coach: Sarah Wilson</Text>
            </View>
          </View>
        </View>
      ))}
    </ScrollView>
  );

  const renderTournamentTab = () => (
    <ScrollView style={styles.tabContent}>
      <View style={styles.tournamentHeader}>
        <Text style={styles.sectionTitle}>In-Camp Tournaments</Text>
        <TouchableOpacity style={styles.createTournamentButton}>
          <MaterialIcons name="emoji-events" size={20} color="#fff" />
          <Text style={styles.createTournamentText}>Create In-Camp Tournament</Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.tournamentCard}>
        <View style={styles.tournamentInfo}>
          <Text style={styles.tournamentName}>Skills Competition</Text>
          <Text style={styles.tournamentDate}>Day 3 - August 17, 2025</Text>
          <Text style={styles.tournamentParticipants}>16 participants</Text>
        </View>
        <TouchableOpacity style={styles.manageTournamentButton}>
          <Text style={styles.manageTournamentText}>Manage</Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.tournamentCard}>
        <View style={styles.tournamentInfo}>
          <Text style={styles.tournamentName}>Mini Championship</Text>
          <Text style={styles.tournamentDate}>Day 5 - August 19, 2025</Text>
          <Text style={styles.tournamentParticipants}>24 participants</Text>
        </View>
        <TouchableOpacity style={styles.manageTournamentButton}>
          <Text style={styles.manageTournamentText}>Manage</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );

  const renderCoachProfileTab = () => (
    <ScrollView style={styles.tabContent}>
      <View style={styles.coachProfileContainer}>
        <Image
          source={{ uri: "https://picsum.photos/200/200?random=10" }}
          style={styles.coachAvatar}
        />
        <Text style={styles.coachName}>Mike Johnson</Text>
        <Text style={styles.coachTitle}>Head Coach & Skills Development Specialist</Text>
        <Text style={styles.coachBio}>
          Mike Johnson has 15 years of experience coaching hockey at all levels. Specializes in power skating, puck handling, shooting techniques, and game strategy.
        </Text>
        <View style={styles.coachDetails}>
          <Text style={styles.coachDetailItem}>• USA Hockey Level 4 Certification</Text>
          <Text style={styles.coachDetailItem}>• Coached 3 championship teams</Text>
          <Text style={styles.coachDetailItem}>• Former NHL Player</Text>
        </View>
      </View>
    </ScrollView>
  );

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={24} color="#666" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>{campName}</Text>
          <TouchableOpacity onPress={() => setShowMessageModal(true)}>
            <MaterialIcons name="message" size={24} color="#D52B1E" />
          </TouchableOpacity>
        </View>

        <View style={styles.tabNavigation}>
          <TouchableOpacity
            style={[styles.navTab, activeTab === "attendees" && styles.activeNavTab]}
            onPress={() => setActiveTab("attendees")}
          >
            <Text style={[styles.navTabText, activeTab === "attendees" && styles.activeNavTabText]}>
              Attendees
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.navTab, activeTab === "schedule" && styles.activeNavTab]}
            onPress={() => setActiveTab("schedule")}
          >
            <Text style={[styles.navTabText, activeTab === "schedule" && styles.activeNavTabText]}>
              Schedule
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.navTab, activeTab === "tournament" && styles.activeNavTab]}
            onPress={() => setActiveTab("tournament")}
          >
            <Text style={[styles.navTabText, activeTab === "tournament" && styles.activeNavTabText]}>
              In-Camp Tournaments
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.navTab, activeTab === "coach" && styles.activeNavTab]}
            onPress={() => setActiveTab("coach")}
          >
            <Text style={[styles.navTabText, activeTab === "coach" && styles.activeNavTabText]}>
              Coach Profile
            </Text>
          </TouchableOpacity>
        </View>

        {activeTab === "attendees" && (
          <View style={styles.attendeesContainer}>
            <View style={styles.searchContainer}>
              <Ionicons name="search" size={20} color="#666" />
              <TextInput
                style={styles.searchInput}
                placeholder="Search attendees..."
                value={searchQuery}
                onChangeText={setSearchQuery}
              />
            </View>
            
            <ScrollView style={styles.attendeesList}>
              {filteredAttendees.map(renderAttendeeCard)}
            </ScrollView>
          </View>
        )}

        {activeTab === "schedule" && renderScheduleTab()}
        {activeTab === "tournament" && renderTournamentTab()}
        {activeTab === "coach" && renderCoachProfileTab()}

        {/* Message Modal */}
        <Modal visible={showMessageModal} transparent animationType="fade">
          <View style={styles.messageModalOverlay}>
            <View style={styles.messageModal}>
              <Text style={styles.messageModalTitle}>Send Message to All Attendees</Text>
              <TextInput
                style={styles.messageInput}
                placeholder="Type your message here..."
                multiline
                numberOfLines={4}
                value={messageText}
                onChangeText={setMessageText}
              />
              <View style={styles.messageModalButtons}>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={() => setShowMessageModal(false)}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.sendButton} onPress={sendMessage}>
                  <Text style={styles.sendButtonText}>Send</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#E5E5E7",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#1a1a1a",
  },
  tabNavigation: {
    flexDirection: "row",
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#E5E5E7",
  },
  navTab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: "center",
  },
  activeNavTab: {
    borderBottomWidth: 2,
    borderBottomColor: "#D52B1E",
  },
  navTabText: {
    fontSize: 16,
    color: "#666",
  },
  activeNavTabText: {
    color: "#D52B1E",
    fontWeight: "600",
  },
  attendeesContainer: {
    flex: 1,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#fff",
    margin: 16,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#E5E5E7",
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
  },
  attendeesList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  attendeeCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  attendeeHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 12,
  },
  attendeeInfo: {
    flexDirection: "row",
    flex: 1,
  },
  avatarContainer: {
    marginRight: 12,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  avatarPlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: "#D52B1E",
    justifyContent: "center",
    alignItems: "center",
  },
  avatarText: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "bold",
  },
  attendeeDetails: {
    flex: 1,
  },
  attendeeName: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#1a1a1a",
    marginBottom: 4,
  },
  attendeeAge: {
    fontSize: 14,
    color: "#666",
    marginBottom: 2,
  },
  attendeeEmail: {
    fontSize: 14,
    color: "#666",
  },
  paymentStatus: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  paymentStatusText: {
    fontSize: 10,
    fontWeight: "bold",
    color: "#fff",
  },
  attendeeActions: {
    flexDirection: "row",
    justifyContent: "space-between",
    borderTopWidth: 1,
    borderTopColor: "#f0f0f0",
    paddingTop: 12,
  },
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
    paddingVertical: 8,
  },
  actionButtonText: {
    marginLeft: 4,
    fontSize: 14,
    color: "#666",
  },
  tabContent: {
    flex: 1,
    paddingHorizontal: 16,
  },
  scheduleHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#1a1a1a",
  },
  addScheduleButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#D52B1E",
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  addScheduleText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
    marginLeft: 4,
  },
  dayCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  dayTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#1a1a1a",
    marginBottom: 12,
  },
  sessionsList: {
    gap: 8,
  },
  session: {
    backgroundColor: "#f8f9fa",
    padding: 12,
    borderRadius: 8,
  },
  sessionTime: {
    fontSize: 14,
    fontWeight: "600",
    color: "#D52B1E",
  },
  sessionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1a1a1a",
    marginVertical: 4,
  },
  sessionCoach: {
    fontSize: 14,
    color: "#666",
  },
  tournamentHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 16,
  },
  createTournamentButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#D52B1E",
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  createTournamentText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
    marginLeft: 4,
  },
  tournamentCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tournamentInfo: {
    flex: 1,
  },
  tournamentName: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#1a1a1a",
    marginBottom: 4,
  },
  tournamentDate: {
    fontSize: 14,
    color: "#666",
    marginBottom: 2,
  },
  tournamentParticipants: {
    fontSize: 14,
    color: "#666",
  },
  manageTournamentButton: {
    backgroundColor: "#D52B1E",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  manageTournamentText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
  },
  messageModalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  messageModal: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 20,
    width: "90%",
    maxWidth: 400,
  },
  messageModalTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#1a1a1a",
    marginBottom: 16,
    textAlign: "center",
  },
  messageInput: {
    borderWidth: 1,
    borderColor: "#E5E5E7",
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    textAlignVertical: "top",
    marginBottom: 16,
    minHeight: 100,
  },
  messageModalButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: "#f0f0f0",
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: "center",
  },
  cancelButtonText: {
    fontSize: 16,
    color: "#666",
    fontWeight: "600",
  },
  sendButton: {
    flex: 1,
    backgroundColor: "#D52B1E",
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: "center",
  },
  sendButtonText: {
    fontSize: 16,
    color: "#fff",
    fontWeight: "600",
  },
  coachProfileContainer: {
    alignItems: "center",
    padding: 24,
  },
  coachAvatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: 16,
  },
  coachName: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#1a1a1a",
    marginBottom: 4,
  },
  coachTitle: {
    fontSize: 16,
    color: "#666",
    marginBottom: 12,
  },
  coachBio: {
    fontSize: 14,
    color: "#444",
    textAlign: "center",
    marginBottom: 16,
  },
  coachDetails: {
    alignItems: "flex-start",
    marginTop: 8,
  },
  coachDetailItem: {
    fontSize: 14,
    color: "#666",
    marginBottom: 4,
  },
});
