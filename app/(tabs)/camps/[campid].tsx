import React, { useState, useEffect } from "react";
import { useLocalSearchParams, useRouter } from "expo-router";
import { getCampById, type Camp } from "../../../data/camps/camps";
import CoachProfile from "./components/CoachProfile";
import CampRegistration from "./components/CampRegistration";
import CampTabs from "./components/CampTabs";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
} from "react-native";
import { Ionicons, MaterialIcons } from "@expo/vector-icons";

export const navigationOptions = {
  headerShown: false,
};

const campTabs = [
  { key: "attendees", label: "Attendees" },
  { key: "schedule", label: "Schedule" },
  { key: "tournaments", label: "Tournaments" },
  { key: "coaches", label: "Coaches" },
];

export default function CampDynamicPage() {
  const router = useRouter();
  const { campid } = useLocalSearchParams();
  const [camp, setCamp] = useState<Camp | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeSubTab, setActiveSubTab] = useState("attendees");
  const [selectedCoach, setSelectedCoach] = useState<any>(null);
  const [showCoachProfile, setShowCoachProfile] = useState(false);

  useEffect(() => {
    if (campid) {
      setLoading(true);
      getCampById(campid as string)
        .then((foundCamp) => {
          setCamp(foundCamp || null);
        })
        .catch((err) => {
          console.error("Error in getCampById:", err);
          setCamp(null);
        })
        .finally(() => {
          setLoading(false);
        });
    } else {
      setCamp(null);
      setLoading(false);
    }
  }, [campid]);

  const renderTabs = () => (
    <View style={styles.tabsRow}>
      {campTabs.map((tab) => (
        <TouchableOpacity
          key={tab.key}
          style={[
            styles.tabButton,
            activeSubTab === tab.key && styles.tabButtonActive,
          ]}
          onPress={() => setActiveSubTab(tab.key)}
        >
          <Text
            style={[
              styles.tabButtonText,
              activeSubTab === tab.key && styles.tabButtonTextActive,
            ]}
          >
            {tab.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  // --- Sub Tab Content ---
  const renderAttendeesContent = () => {
    if (!camp) {
      return (
        <View style={styles.emptyContainer}>
          <MaterialIcons name="group-add" size={64} color="#D52B1E" />
          <Text style={styles.emptyTitle}>Camp Not Found</Text>
          <Text style={styles.emptyText}>
            This camp could not be found.
          </Text>
        </View>
      );
    }
    // Replace with real attendees data if available
    const attendees = [
      { id: 1, name: "John Smith", age: 16, position: "Forward", email: "<EMAIL>", status: "Registered" },
      { id: 2, name: "Emily Johnson", age: 15, position: "Defense", email: "<EMAIL>", status: "Waitlist" },
    ];
    return (
      <View style={styles.contentContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Camp Attendees</Text>
          <Text style={styles.sectionSubtitle}>{attendees.length} registered players</Text>
        </View>
        {attendees.map((attendee) => (
          <View key={attendee.id} style={styles.attendeeCard}>
            <View style={styles.attendeeInfo}>
              <Text style={styles.attendeeName}>{attendee.name}</Text>
              <Text style={styles.attendeeDetails}>Age: {attendee.age} | Position: {attendee.position}</Text>
              <Text style={styles.attendeeEmail}>{attendee.email}</Text>
            </View>
            <View style={[
              styles.statusBadge,
              { backgroundColor: attendee.status === "Registered" ? "#4CAF50" : "#FF9800" }
            ]}>
              <Text style={styles.statusText}>{attendee.status}</Text>
            </View>
          </View>
        ))}
      </View>
    );
  };

  const renderScheduleContent = () => {
    if (!camp) {
      return (
        <View style={styles.emptyContainer}>
          <MaterialIcons name="schedule" size={64} color="#D52B1E" />
          <Text style={styles.emptyTitle}>Camp Not Found</Text>
          <Text style={styles.emptyText}>
            This camp could not be found.
          </Text>
        </View>
      );
    }
    // Replace with real schedule data if available
    const schedule = [
      { time: "09:00 - 10:00", activity: "Warm-up & Skating Drills", instructor: "Coach Smith" },
      { time: "10:15 - 11:15", activity: "Puck Handling Skills", instructor: "Coach Johnson" },
    ];
    return (
      <View style={styles.contentContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Daily Schedule</Text>
          <Text style={styles.sectionSubtitle}>Training activities and timing</Text>
        </View>
        {schedule.map((item, index) => (
          <View key={index} style={styles.scheduleCard}>
            <View style={styles.timeContainer}>
              <Text style={styles.timeText}>{item.time}</Text>
            </View>
            <View style={styles.activityContainer}>
              <Text style={styles.activityTitle}>{item.activity}</Text>
              <Text style={styles.instructorText}>Instructor: {item.instructor}</Text>
            </View>
          </View>
        ))}
      </View>
    );
  };

  const renderTournamentsContent = () => {
    if (!camp) {
      return (
        <View style={styles.emptyContainer}>
          <MaterialIcons name="emoji-events" size={64} color="#D52B1E" />
          <Text style={styles.emptyTitle}>Camp Not Found</Text>
          <Text style={styles.emptyText}>
            This camp could not be found.
          </Text>
        </View>
      );
    }
    // Replace with real tournaments data if available
    const tournaments = [
      { name: "Skills Challenge", date: "Friday", participants: 24, status: "Upcoming" },
      { name: "3v3 Tournament", date: "Saturday", participants: 18, status: "In Progress" },
    ];
    return (
      <View style={styles.contentContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>In-Camp Tournaments</Text>
          <Text style={styles.sectionSubtitle}>Competitive events and challenges</Text>
        </View>
        {tournaments.map((tournament, index) => (
          <View key={index} style={styles.tournamentCard}>
            <View style={styles.tournamentInfo}>
              <Text style={styles.tournamentName}>{tournament.name}</Text>
              <Text style={styles.tournamentDate}>{tournament.date}</Text>
              <Text style={styles.tournamentParticipants}>{tournament.participants} participants</Text>
            </View>
            <View style={[
              styles.statusBadge,
              {
                backgroundColor:
                  tournament.status === "Completed"
                    ? "#4CAF50"
                    : tournament.status === "In Progress"
                    ? "#FF9800"
                    : "#2196F3",
              },
            ]}>
              <Text style={styles.statusText}>{tournament.status}</Text>
            </View>
          </View>
        ))}
      </View>
    );
  };

  const renderCoachesContent = () => {
    if (!camp) {
      return (
        <View style={styles.emptyContainer}>
          <MaterialIcons name="person" size={64} color="#D52B1E" />
          <Text style={styles.emptyTitle}>Camp Not Found</Text>
          <Text style={styles.emptyText}>
            This camp could not be found.
          </Text>
        </View>
      );
    }
    // Replace with real coach data if available
    const coach = {
      name: camp.coach || "Coach Name",
      title: "Head Coach & Skills Development Specialist",
      experience: "15 years",
      specialization: ["Power Skating", "Puck Handling", "Shooting Techniques", "Game Strategy"],
      bio: "Professional hockey coach with extensive experience in player development.",
      achievements: [
        "Former NHL Player - Boston Bruins (5 years)",
        "USA Hockey Level 4 Certification",
        "Coached 3 championship teams"
      ],
      contact: {
        email: "<EMAIL>",
        phone: "555-0123"
      },
      avatar: camp.coachImage,
      rating: 4.9,
      campsLed: 47
    };
    return (
      <View style={styles.coachesGrid}>
        <TouchableOpacity
          style={styles.coachCard}
          onPress={() => {
            setSelectedCoach(coach);
            setShowCoachProfile(true);
          }}
        >
          <Image source={{ uri: coach.avatar }} style={styles.coachCardImage} />
          <Text style={styles.coachCardName}>{coach.name}</Text>
          <Text style={styles.coachCardTitle}>{coach.title}</Text>
        </TouchableOpacity>
        {/* CoachProfile modal */}
        {selectedCoach && (
          <CoachProfile
            coach={selectedCoach}
            visible={showCoachProfile}
            onClose={() => setShowCoachProfile(false)}
          />
        )}
      </View>
    );
  };

  const renderContent = () => {
    switch (activeSubTab) {
      case "attendees":
        return renderAttendeesContent();
      case "schedule":
        return renderScheduleContent();
      case "tournaments":
        return renderTournamentsContent();
      case "coaches":
        return renderCoachesContent();
      default:
        return renderAttendeesContent();
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Loading camp...</Text>
      </View>
    );
  }

  if (!camp) {
    return (
      <View style={styles.emptyContainer}>
        <MaterialIcons name="error-outline" size={64} color="#D52B1E" />
        <Text style={styles.emptyTitle}>Camp Not Found</Text>
        <Text style={styles.emptyText}>
          The camp you are looking for does not exist.
        </Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => router.back()}
        activeOpacity={0.7}
      >
        <Ionicons name="arrow-back" size={28} color="#D52B1E" />
        <Text style={styles.backButtonText}>All Camps</Text>
      </TouchableOpacity>
      <View style={styles.header}>
        <Text style={styles.title}>{camp.name}</Text>
        <Text style={styles.subtitle}>
          {camp.location} • {camp.startDate} - {camp.endDate}
        </Text>
      </View>
      <CampTabs
        tabs={campTabs}
        selectedTab={activeSubTab}
        onTabSelect={setActiveSubTab}
      />
      {renderContent()}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  header: {
    padding: 24,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#E5E5E7",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#1a1a1a",
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: "#666",
  },
  tabsRow: {
    flexDirection: "row",
    backgroundColor: "#fff",
    paddingHorizontal: 8,
    paddingTop: 8,
    paddingBottom: 4,
    gap: 8, // This creates the gap between tabs
  },
  tabButton: {
    flex: 1,
    alignItems: "center",
    paddingVertical: 10,
    borderRadius: 8,
    backgroundColor: "#F0F1F5",
  },
  tabButtonActive: {
    backgroundColor: "#fff",
    borderBottomWidth: 3,
    borderBottomColor: "#D52B1E",
  },
  tabButtonText: {
    fontSize: 15,
    color: "#666",
    fontWeight: "600",
  },
  tabButtonTextActive: {
    color: "#D52B1E",
  },
  contentContainer: {
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  sectionHeader: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#1a1a1a",
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: "#666",
  },
  attendeeCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  attendeeInfo: {
    flex: 1,
  },
  attendeeName: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#1a1a1a",
    marginBottom: 4,
  },
  attendeeDetails: {
    fontSize: 14,
    color: "#666",
    marginBottom: 2,
  },
  attendeeEmail: {
    fontSize: 12,
    color: "#999",
  },
  scheduleCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: "row",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  timeContainer: {
    marginRight: 16,
    minWidth: 80,
  },
  timeText: {
    fontSize: 12,
    fontWeight: "600",
    color: "#D52B1E",
  },
  activityContainer: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#1a1a1a",
    marginBottom: 4,
  },
  instructorText: {
    fontSize: 14,
    color: "#666",
  },
  tournamentCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tournamentInfo: {
    flex: 1,
  },
  tournamentName: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#1a1a1a",
    marginBottom: 4,
  },
  tournamentDate: {
    fontSize: 14,
    color: "#666",
    marginBottom: 2,
  },
  tournamentParticipants: {
    fontSize: 12,
    color: "#999",
  },
  coachesGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    paddingHorizontal: 16,
    paddingVertical: 16,
    gap: 12,
  },
  coachCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    width: "48%",
  },
  coachCardImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginBottom: 8,
  },
  coachCardName: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#1a1a1a",
    marginBottom: 2,
    textAlign: "center",
  },
  coachCardTitle: {
    fontSize: 12,
    color: "#666",
    marginBottom: 4,
    textAlign: "center",
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: "600",
    color: "#fff",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 40,
  },
  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
    backgroundColor: "#fff",
    borderRadius: 16,
    marginVertical: 20,
    marginHorizontal: 16,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#1a1a1a",
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: "#999",
    textAlign: "center",
    lineHeight: 20,
  },
  backButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    backgroundColor: "#fff",
  },
  backButtonText: {
    marginLeft: 8,
    color: "#D52B1E",
    fontSize: 16,
    fontWeight: "600",
  },
});