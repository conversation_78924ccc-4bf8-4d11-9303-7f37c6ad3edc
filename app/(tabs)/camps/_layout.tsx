import { Stack } from "expo-router";

export const navigationOptions = {
  headerShown: false,
};

export default function EducationLayout() {
  return (
    <Stack>
      <Stack.Screen
        name="index"
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="[campid]"
        options={{
          headerShown: false, // <-- Hide header for dynamic camp page
        }}
      />
      <Stack.Screen
        name="camps"
        options={{
          title: "Hockey Camps",
          headerBackTitle: "Back",
          headerStyle: {
            backgroundColor: "#D52B1E",
          },
          headerTintColor: "#fff",
          headerTitleStyle: {
            fontSize: 20,
          },
        }}
      />
      <Stack.Screen
        name="showcases"
        options={{
          title: "Showcases",
          headerBackTitle: "Back",
          headerStyle: {
            backgroundColor: "#D52B1E",
          },
          headerTintColor: "#fff",
          headerTitleStyle: {
            fontSize: 20,
          },
        }}
      />
    </Stack>
  );
}
