import React from "react";
import { View, Text, StyleSheet, ScrollView } from "react-native";
import { useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import Header from "./components/Header";
import FavouriteTeamCard from "./components/FavouriteTeamCard";

const favouriteTeams = [
  {
    id: "1",
    name: "Florida Panthers",
    league: "NHL",
    division: "Atlantic",
    logo: "",
  },
  {
    id: "2",
    name: "Columbus Blue Jackets",
    league: "NHL",
    division: "Metropolitan",
    logo: "",
  },
  {
    id: "3",
    name: "Hong Kong Dragons",
    league: "HKPHL",
    division: "Division A",
    logo: "",
  },
];

export default function FavouritesScreen() {
  const router = useRouter();

  return (
    <View style={styles.container}>
      <Header title="Favourites" />
      <ScrollView style={styles.scrollContent}>
        <View style={styles.content}>
          {favouriteTeams.map((team) => (
            <FavouriteTeamCard
              key={team.id}
              team={team}
              onPress={() =>
                router.push(`/favourites/team-details?id=${team.id}`)
              }
            />
          ))}

          {favouriteTeams.length === 0 && (
            <View style={styles.emptyState}>
              <Ionicons name="heart-outline" size={48} color="#8E8E93" />
              <Text style={styles.emptyText}>No favourite teams yet</Text>
              <Text style={styles.emptySubtext}>
                Add teams to your favourites to see them here
              </Text>
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  scrollContent: {
    flex: 1,
  },
  content: {
    paddingTop: 15,
  },
  emptyState: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 48,
    paddingHorizontal: 16,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: "600",
    color: "#8E8E93",
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: "#8E8E93",
    textAlign: "center",
  },
});
