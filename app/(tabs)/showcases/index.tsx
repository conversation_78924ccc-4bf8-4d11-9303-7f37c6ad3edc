import React, { useState, useEffect } from "react";
import {
  View,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  Alert,
  ActivityIndicator,
  RefreshControl,
  Text,
} from "react-native";
import Header from "./components/Header";
import CountryTabs from "./components/CountryTabs";
import ShowcaseCard from "./components/ShowcasesCard";
import AddShowcaseModal from "./components/AddshowcasesModal";
import {
  fetchShowcases,
  countries,
  ShowcaseDetails,
} from "../../../data/showcases/showcases";

export default function ShowcasesScreen() {
  const [selectedCountry, setSelectedCountry] = useState("All");
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  const [editingShowcase, setEditingShowcase] =
    useState<ShowcaseDetails | null>(null);
  const [showcases, setShowcases] = useState<ShowcaseDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadShowcases();
  }, []);

  const loadShowcases = async () => {
    setLoading(true);
    setError(null);
    try {
      const fetchedShowcases = await fetchShowcases();
      console.log("Fetched showcases count:", fetchedShowcases.length);
      console.log(
        "First showcase structure:",
        JSON.stringify(fetchedShowcases[0], null, 2)
      );
      setShowcases(fetchedShowcases);
    } catch (error) {
      console.error("Error loading showcases:", error);
      setError("Failed to load showcases");
      Alert.alert("Error", "Failed to load showcases");
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadShowcases();
    setRefreshing(false);
  };

  const filteredShowcases = showcases.filter(
    (showcase) =>
      selectedCountry === "All" || showcase.country === selectedCountry
  );

  console.log("Filtered showcases:", filteredShowcases); // Debug log

  const handleFilterPress = () => {
    Alert.alert(
      "Filter Options",
      "Advanced filtering options will be available here",
      [{ text: "OK", style: "default" }]
    );
  };

  const handleAddShowcase = () => {
    setEditingShowcase(null);
    setIsAddModalVisible(true);
  };

  const handleEditShowcase = (showcase: ShowcaseDetails) => {
    setEditingShowcase(showcase);
    setIsAddModalVisible(true);
  };

  const handleCloseModal = () => {
    setIsAddModalVisible(false);
    setEditingShowcase(null);
    // Refresh showcases after adding/editing
    loadShowcases();
  };

  const renderContent = () => {
    if (loading) {
      return (
        <ActivityIndicator size="large" color="#D52B1E" style={styles.loader} />
      );
    }

    if (error) {
      return (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <Text style={styles.retryText} onPress={loadShowcases}>
            Tap to retry
          </Text>
        </View>
      );
    }

    if (filteredShowcases.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>
            {selectedCountry === "All"
              ? "No showcases found"
              : `No showcases found in ${selectedCountry}`}
          </Text>
        </View>
      );
    }

    return filteredShowcases.map((showcase) => {
      // Debug log for each showcase
      console.log("Rendering showcase:", {
        id: showcase.id,
        title: showcase.title,
        subtitle: showcase.subtitle,
        date: showcase.date,
        hasTitle: !!showcase.title,
        hasSubtitle: !!showcase.subtitle,
        hasDate: !!showcase.date,
      });

      return (
        <View key={showcase.id} style={styles.showcaseWrapper}>
          <ShowcaseCard
            showcase={showcase}
            onEdit={handleEditShowcase}
          />
        </View>
      );
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title="Showcases"
        onFilterPress={handleFilterPress}
        onAddPress={handleAddShowcase}
        showFilter={true}
        showAdd={true}
      />

      <CountryTabs
        countries={countries}
        selectedCountry={selectedCountry}
        onCountrySelect={setSelectedCountry}
      />

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {renderContent()}
      </ScrollView>

      <AddShowcaseModal
        visible={isAddModalVisible}
        onClose={handleCloseModal}
        editingShowcase={editingShowcase}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 10,
    paddingVertical: 16,
  },
  loader: {
    marginTop: 50,
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 50,
  },
  errorText: {
    fontSize: 16,
    color: "#D52B1E",
    textAlign: "center",
    marginBottom: 10,
  },
  retryText: {
    fontSize: 14,
    color: "#007AFF",
    textAlign: "center",
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 50,
  },
  emptyText: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
  },
  showcaseWrapper: {
    marginBottom: 12,
  },
});
