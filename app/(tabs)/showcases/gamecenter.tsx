import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  SafeAreaView,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";

// Game data matching the design specification
const game = {
  gameNumber: 1,
  date: "Tu<PERSON>, 3 Jun",
  teams: [
    {
      name: "Panthers",
      fullName: "Florida Panthers",
      logo: "",
      score: 4,
      periodScores: [1, 1, 1, 1],
    },
    {
      name: "Oilers",
      fullName: "Edmonton Oilers",
      logo: "",
      score: 3,
      periodScores: [2, 1, 0, 0],
    },
  ],
};

// Roster data matching the design specification
const rosterData: {
  [key: string]: Array<{
    number: number;
    name: string;
    position: string;
    gp: number | null;
    g: number | null;
    a: number | null;
  }>;
} = {
  Oilers: [
    { number: 10, name: "<PERSON><PERSON> <PERSON>", position: "C", gp: null, g: null, a: null },
    { number: 13, name: "<PERSON><PERSON>", position: "C", gp: 18, g: 3, a: 1 },
    { number: 18, name: "<PERSON><PERSON>", position: "LW", gp: 15, g: 5, a: 6 },
    { number: 19, name: "A. <PERSON>que", position: "C", gp: 18, g: 4, a: 2 },
    { number: 21, name: "T. Frederic", position: "C", gp: 18, g: 1, a: 3 },
    {
      number: 22,
      name: "M. Savoie",
      position: "C",
      gp: null,
      g: null,
      a: null,
    },
    { number: 28, name: "C. <PERSON>", position: "RW", gp: 16, g: 5, a: 3 },
  ],
  Panthers: [
    { number: 16, name: "A. Barkov", position: "C", gp: 18, g: 8, a: 12 },
    { number: 19, name: "M. Tkachuk", position: "LW", gp: 18, g: 6, a: 9 },
    { number: 23, name: "C. Reinhart", position: "C", gp: 18, g: 4, a: 8 },
    { number: 27, name: "E. Rodrigues", position: "C", gp: 15, g: 3, a: 5 },
    { number: 70, name: "A. Lundell", position: "C", gp: 18, g: 2, a: 6 },
    { number: 13, name: "S. Bennett", position: "C", gp: 16, g: 5, a: 3 },
    { number: 17, name: "N. Cousins", position: "C", gp: 12, g: 1, a: 2 },
  ],
};

// Goalie data matching the design specification
const goalieData: {
  [key: string]: Array<{
    number: number;
    name: string;
    sv: number | null; // Save percentage
    gp: number | null; // Games played
    gaa: number | null; // Goals against average
    a: number | null; // Assists
  }>;
} = {
  Oilers: [
    { number: 30, name: "C. Pickard", sv: 0.915, gp: 23, gaa: 2.45, a: 1 },
    { number: 40, name: "S. Skinner", sv: 0.905, gp: 41, gaa: 2.62, a: null },
  ],
  Panthers: [
    { number: 70, name: "S. Bobrovsky", sv: 0.915, gp: 58, gaa: 2.37, a: 2 },
    { number: 33, name: "A. Stolarz", sv: null, gp: null, gaa: null, a: null },
  ],
};

// Game info data
const gameInfo = {
  referee: "Danial Utegenov",
  edmCoach: "Solomon Kim",
  flaCoach: "Vlad Yun",
};

// Shield placeholder component for missing team logos
function ShieldIcon() {
  return (
    <View style={styles.shieldContainer}>
      <View style={styles.shieldShape} />
    </View>
  );
}

// Team logo component with shield fallback
function TeamLogoOrShield({ logo }: { logo?: string }) {
  if (!logo) {
    return <ShieldIcon />;
  }

  return (
    <Image
      source={{ uri: logo }}
      style={styles.teamLogo}
      onError={() => {
        // In a real implementation, you might want to set a state to show shield
        // For now, the Image component will handle the error gracefully
      }}
    />
  );
}

// Segmented Control Component
interface SegmentedControlProps {
  options: string[];
  selectedIndex: number;
  onSelectionChange: (index: number) => void;
}

function SegmentedControl({
  options,
  selectedIndex,
  onSelectionChange,
}: SegmentedControlProps) {
  return (
    <View style={styles.segmentedControl}>
      {options.map((option: string, index: number) => (
        <TouchableOpacity
          key={option}
          style={[
            styles.segmentedOption,
            selectedIndex === index && styles.segmentedOptionSelected,
          ]}
          onPress={() => onSelectionChange(index)}
        >
          <Text
            style={[
              styles.segmentedText,
              selectedIndex === index && styles.segmentedTextSelected,
            ]}
          >
            {option}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
}

export default function GamecenterScreen() {
  const router = useRouter();
  const [selectedTeamIndex, setSelectedTeamIndex] = useState(0);
  const teamOptions = ["Oilers", "Panthers"];
  const selectedTeam = teamOptions[selectedTeamIndex];
  const currentRoster = rosterData[selectedTeam];
  const currentGoalies = goalieData[selectedTeam];

  return (
    <SafeAreaView style={styles.safeArea}>
      {/* Gamecenter Header */}
      <View style={styles.headerContainer}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="chevron-back" size={24} color="#D52B1E" />
          <Text style={styles.backButtonText}>All games</Text>
        </TouchableOpacity>
        <Text style={styles.gamecenterTitle}>Gamecenter</Text>
      </View>

      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Game Card */}
        <View style={styles.card}>
          <Text style={styles.gameDate}>{game.date}</Text>

          <View style={styles.frameParent}>
            <View style={styles.teamContainer}>
              <TeamLogoOrShield logo={game.teams[0].logo} />
              <Text style={styles.scoreTeamName}>{game.teams[0].name}</Text>
            </View>

            <View style={styles.scoreGroup}>
              <Text style={styles.scoreText}>{game.teams[0].score}</Text>
              <Text style={styles.scoreDash}>-</Text>
              <Text style={styles.scoreText}>{game.teams[1].score}</Text>
            </View>

            <View style={styles.teamContainer}>
              <TeamLogoOrShield logo={game.teams[1].logo} />
              <Text style={styles.scoreTeamName}>{game.teams[1].name}</Text>
            </View>
          </View>

          {/* Divider */}
          <View style={styles.divider} />

          {/* Period Table */}
          <View style={styles.periodTable}>
            <View style={styles.periodTableContainer}>
              {/* Team Column */}
              <View style={styles.periodTeamColumn}>
                <Text style={styles.periodHeaderText}>Team</Text>
                <Text style={styles.teamNameText}>
                  {game.teams[0].fullName}
                </Text>
                <Text style={styles.teamNameText}>
                  {game.teams[1].fullName}
                </Text>
              </View>

              {/* Period Columns */}
              <View style={styles.periodColumnsContainer}>
                {/* Period 1 */}
                <View style={styles.periodColumn}>
                  <Text style={styles.periodHeaderText}>1</Text>
                  <Text style={styles.periodScoreText}>
                    {game.teams[0].periodScores[0]}
                  </Text>
                  <Text style={styles.periodScoreText}>
                    {game.teams[1].periodScores[0]}
                  </Text>
                </View>

                {/* Period 2 */}
                <View style={styles.periodColumn}>
                  <Text style={styles.periodHeaderText}>2</Text>
                  <Text style={styles.periodScoreText}>
                    {game.teams[0].periodScores[1]}
                  </Text>
                  <Text style={styles.periodScoreText}>
                    {game.teams[1].periodScores[1]}
                  </Text>
                </View>

                {/* Period 3 */}
                <View style={styles.periodColumn}>
                  <Text style={styles.periodHeaderText}>3</Text>
                  <Text style={styles.periodScoreText}>
                    {game.teams[0].periodScores[2]}
                  </Text>
                  <Text style={styles.periodScoreText}>
                    {game.teams[1].periodScores[2]}
                  </Text>
                </View>

                {/* Overtime */}
                <View style={styles.periodColumn}>
                  <Text style={styles.periodHeaderText}>OT</Text>
                  <Text style={styles.periodScoreText}>
                    {game.teams[0].periodScores[3]}
                  </Text>
                  <Text style={styles.periodScoreText}>
                    {game.teams[1].periodScores[3]}
                  </Text>
                </View>

                {/* Total */}
                <View style={styles.periodColumn}>
                  <Text style={styles.periodHeaderText}>T</Text>
                  <Text style={styles.periodScoreText}>
                    {game.teams[0].score}
                  </Text>
                  <Text style={styles.periodScoreText}>
                    {game.teams[1].score}
                  </Text>
                </View>
              </View>
            </View>
          </View>

          {/* Divider */}
          <View style={styles.divider} />
        </View>

        {/* Roster Card */}
        <View style={styles.card}>
          <Text style={styles.rosterTitle}>Roster</Text>

          <SegmentedControl
            options={teamOptions}
            selectedIndex={selectedTeamIndex}
            onSelectionChange={setSelectedTeamIndex}
          />

          <View style={styles.rosterTable}>
            <View style={styles.rosterTableContainer}>
              {/* Left Side Columns - Player Info */}
              <View style={styles.rosterLeftColumns}>
                {/* Number Column */}
                <View style={styles.rosterNumberColumn}>
                  <Text style={styles.rosterHeaderText}>#</Text>
                  {currentRoster.map((player) => (
                    <Text
                      key={`num-${player.number}`}
                      style={styles.rosterDataText}
                    >
                      {player.number}
                    </Text>
                  ))}
                </View>

                {/* Player Name Column */}
                <View style={styles.rosterNameColumn}>
                  <Text style={styles.rosterHeaderText}>FORWARDS</Text>
                  {currentRoster.map((player) => (
                    <Text
                      key={`name-${player.number}`}
                      style={styles.rosterDataText}
                    >
                      {player.name}
                    </Text>
                  ))}
                </View>
              </View>

              {/* Right Side Columns - Stats */}
              <View style={styles.rosterRightColumns}>
                {/* Position Column */}
                <View style={styles.rosterStatColumn}>
                  <Text style={styles.rosterHeaderText}>POS</Text>
                  {currentRoster.map((player) => (
                    <Text
                      key={`pos-${player.number}`}
                      style={styles.rosterDataText}
                    >
                      {player.position}
                    </Text>
                  ))}
                </View>

                {/* Games Played Column */}
                <View style={styles.rosterStatColumn}>
                  <Text style={styles.rosterHeaderText}>GP</Text>
                  {currentRoster.map((player) => (
                    <Text
                      key={`gp-${player.number}`}
                      style={styles.rosterDataText}
                    >
                      {player.gp !== null ? player.gp : "-"}
                    </Text>
                  ))}
                </View>

                {/* Goals Column */}
                <View style={styles.rosterStatColumn}>
                  <Text style={styles.rosterHeaderText}>G</Text>
                  {currentRoster.map((player) => (
                    <Text
                      key={`g-${player.number}`}
                      style={styles.rosterDataText}
                    >
                      {player.g !== null ? player.g : "-"}
                    </Text>
                  ))}
                </View>

                {/* Assists Column */}
                <View style={styles.rosterStatColumn}>
                  <Text style={styles.rosterHeaderText}>A</Text>
                  {currentRoster.map((player) => (
                    <Text
                      key={`a-${player.number}`}
                      style={styles.rosterDataText}
                    >
                      {player.a !== null ? player.a : "-"}
                    </Text>
                  ))}
                </View>
              </View>
            </View>
          </View>

          {/* Divider */}
          <View style={styles.divider} />

          {/* Goalie Roster Table */}
          <View style={styles.rosterTable}>
            <View style={styles.rosterTableContainer}>
              {/* Left Side Columns - Goalie Info */}
              <View style={styles.rosterLeftColumns}>
                {/* Number Column */}
                <View style={styles.rosterNumberColumn}>
                  <Text style={styles.rosterHeaderText}>#</Text>
                  {currentGoalies.map((goalie) => (
                    <Text
                      key={`goalie-num-${goalie.number}`}
                      style={styles.rosterDataText}
                    >
                      {goalie.number}
                    </Text>
                  ))}
                </View>

                {/* Goalie Name Column */}
                <View style={styles.rosterNameColumn}>
                  <Text style={styles.rosterHeaderText}>GOALIE</Text>
                  {currentGoalies.map((goalie) => (
                    <Text
                      key={`goalie-name-${goalie.number}`}
                      style={styles.rosterDataText}
                    >
                      {goalie.name}
                    </Text>
                  ))}
                </View>
              </View>

              {/* Right Side Columns - Goalie Stats */}
              <View style={styles.rosterRightColumns}>
                {/* Save Percentage Column */}
                <View style={styles.rosterStatColumn}>
                  <Text style={styles.rosterHeaderText}>SV%</Text>
                  {currentGoalies.map((goalie) => (
                    <Text
                      key={`goalie-sv-${goalie.number}`}
                      style={styles.rosterDataText}
                    >
                      {goalie.sv !== null ? goalie.sv.toFixed(3) : "-"}
                    </Text>
                  ))}
                </View>

                {/* Games Played Column */}
                <View style={styles.rosterStatColumn}>
                  <Text style={styles.rosterHeaderText}>GP</Text>
                  {currentGoalies.map((goalie) => (
                    <Text
                      key={`goalie-gp-${goalie.number}`}
                      style={styles.rosterDataText}
                    >
                      {goalie.gp !== null ? goalie.gp : "-"}
                    </Text>
                  ))}
                </View>

                {/* Goals Against Average Column */}
                <View style={styles.rosterStatColumn}>
                  <Text style={styles.rosterHeaderText}>GAA</Text>
                  {currentGoalies.map((goalie) => (
                    <Text
                      key={`goalie-gaa-${goalie.number}`}
                      style={styles.rosterDataText}
                    >
                      {goalie.gaa !== null ? goalie.gaa.toFixed(2) : "-"}
                    </Text>
                  ))}
                </View>

                {/* Assists Column */}
                <View style={styles.rosterStatColumn}>
                  <Text style={styles.rosterHeaderText}>A</Text>
                  {currentGoalies.map((goalie) => (
                    <Text
                      key={`goalie-a-${goalie.number}`}
                      style={styles.rosterDataText}
                    >
                      {goalie.a !== null ? goalie.a : "-"}
                    </Text>
                  ))}
                </View>
              </View>
            </View>
          </View>
        </View>

        {/* Game Info Card */}
        <View style={styles.card}>
          <Text style={styles.gameInfoTitle}>Game Info</Text>

          <View style={styles.gameInfoRow}>
            <Text style={styles.gameInfoLabel}>Referee</Text>
            <Text style={styles.gameInfoValue}>{gameInfo.referee}</Text>
          </View>

          <View style={styles.gameInfoRow}>
            <Text style={styles.gameInfoLabel}>EDM Coach</Text>
            <Text style={styles.gameInfoValue}>{gameInfo.edmCoach}</Text>
          </View>

          <View style={styles.gameInfoRow}>
            <Text style={styles.gameInfoLabel}>FLA Coach</Text>
            <Text style={styles.gameInfoValue}>{gameInfo.flaCoach}</Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  backButton: {
    flexDirection: "row",
    alignItems: "center",
    marginLeft: -5,
  },
  backButtonText: {
    color: "#D52B1E",
    fontSize: 17,
    marginLeft: 1,
  },
  headerContainer: {
    backgroundColor: "#FFFFFF",
    paddingHorizontal: 16,
    paddingVertical: 10,
    gap: 6,
    borderBottomColor: "#A8A9AB",
    borderBottomWidth: 0.5,
  },
  gamecenterTitle: {
    fontSize: 24,
    color: "#231716",
    fontFamily: "BakbakOne_400Regular",
  },
  container: {
    flex: 1,
    paddingTop: 15,
  },
  card: {
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    marginHorizontal: 10,
    marginBottom: 12,
    padding: 16,
    shadowColor: "#000000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.07,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 0.2,
    borderColor: "#CACDD4",
  },
  gameDate: {
    fontSize: 16,
    color: "#7F7271",
    textAlign: "center",
    marginBottom: 8,
  },
  frameParent: {
    justifyContent: "space-between",
    flexDirection: "row",
    marginBottom: 12,
  },
  teamContainer: {
    gap: 5,
    justifyContent: "center",
    alignItems: "center",
    marginHorizontal: 16,
  },
  teamLogo: {
    width: 45,
    height: 45,
    resizeMode: "contain",
  },
  scoreTeamName: {
    fontSize: 22,
    fontWeight: "400",
    color: "#231716",
    textAlign: "center",
    fontFamily: "Archivo-Regular",
  },
  scoreGroup: {
    gap: 12,
    flexDirection: "row",
    alignItems: "center",
  },
  scoreText: {
    fontSize: 30,
    color: "#231716",
    textAlign: "center",
    fontFamily: "Archivo-Regular",
  },
  scoreDash: {
    width: 14,
    fontSize: 30,
    color: "#231716",
    textAlign: "center",
    fontFamily: "Archivo-Regular",
    justifyContent: "center",
    alignItems: "center",
  },
  divider: {
    height: 1,
    backgroundColor: "#E0E0E0",
    marginVertical: 8,
  },
  periodTable: {
    width: "100%",
    paddingHorizontal: 10,
    paddingVertical: 0,
  },
  periodTableContainer: {
    gap: 0,
    justifyContent: "space-between",
    flexDirection: "row",
    alignItems: "center",
  },
  periodTeamColumn: {
    gap: 10,
    justifyContent: "center",
  },
  periodColumnsContainer: {
    gap: 20,
    flexDirection: "row",
    alignItems: "center",
  },
  periodColumn: {
    gap: 10,
    justifyContent: "center",
    alignItems: "center",
  },
  periodHeaderText: {
    color: "#7f7271",
    textAlign: "left",
    fontFamily: "Archivo-Regular",
    fontSize: 16,
  },
  teamNameText: {
    color: "#231716",
    textAlign: "left",
    fontFamily: "Archivo-Regular",
    fontSize: 16,
  },
  periodScoreText: {
    color: "#231716",
    textAlign: "left",
    fontFamily: "Archivo-Regular",
    fontSize: 16,
  },
  rosterTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#231716",
    marginBottom: 12,
  },
  segmentedControl: {
    flexDirection: "row",
    backgroundColor: "#F2F2F7",
    borderRadius: 8,
    marginBottom: 12,
    padding: 2,
  },
  segmentedOption: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: "center",
  },
  segmentedOptionSelected: {
    backgroundColor: "#FFFFFF",
    shadowColor: "#000000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  segmentedText: {
    fontSize: 14,
    color: "#231716",
  },
  segmentedTextSelected: {
    color: "#231716",
    fontWeight: "600",
    fontSize: 14,
  },
  rosterTable: {
    paddingVertical: 8,
  },
  rosterTableContainer: {
    justifyContent: "space-between",
    flexDirection: "row",
    alignItems: "stretch",
  },
  rosterLeftColumns: {
    gap: 5,
    flexDirection: "row",
    alignItems: "flex-start",
  },
  rosterRightColumns: {
    gap: 2,
    flexDirection: "row",
    alignItems: "flex-start",
  },
  rosterNumberColumn: {
    gap: 17,
    justifyContent: "center",
    alignItems: "center",
    minWidth: 30,
  },
  rosterNameColumn: {
    gap: 17,
    justifyContent: "center",
    alignItems: "flex-start",
    minWidth: 100,
  },
  rosterStatColumn: {
    gap: 17,
    justifyContent: "center",
    alignItems: "center",
    minWidth: 40,
  },
  rosterHeaderText: {
    color: "#7f7271",
    textAlign: "left",
    fontFamily: "Archivo-Regular",
    fontSize: 16,
  },
  rosterDataText: {
    color: "#231716",
    textAlign: "left",
    fontFamily: "Archivo-Regular",
    fontSize: 16,
  },
  gameInfoTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#231716",
    marginBottom: 16,
  },
  gameInfoRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  gameInfoLabel: {
    fontSize: 16,
    color: "#7F7271",
    fontWeight: "400",
  },
  gameInfoValue: {
    fontSize: 16,
    color: "#231716",
  },
  shieldContainer: {
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
  },
  shieldShape: {
    width: 32,
    height: 32,
    backgroundColor: "#E0E0E0",
    borderRadius: 4,
    borderWidth: 2,
    borderColor: "#CCCCCC",
  },
});
