import React from "react";
import { View, Text, StyleSheet, ScrollView, Image } from "react-native";
import { Ionicons, MaterialCommunityIcons } from "@expo/vector-icons";
import { ShowcaseDetails, getStatusColor } from "../../../../data/showcases/showcases";

interface OverviewTabProps {
  showcase: ShowcaseDetails;
}

export default function OverviewTab({ showcase }: OverviewTabProps) {
  return (
    <ScrollView style={styles.tabContainer}>
      <Image
        source={{ uri: showcase.image }}
        style={styles.showcaseImage}
      />

      <View style={styles.infoSection}>
        <Text style={styles.sectionTitle}>Showcase Overview</Text>

        <View style={styles.infoRow}>
          <Ionicons name="calendar" size={20} color="#231716" />
          <Text style={styles.infoText}>{showcase.date}</Text>
        </View>

        <View style={styles.infoRow}>
          <Ionicons name="location-sharp" size={20} color="#231716" />
          <Text style={styles.infoText}>{showcase.location}</Text>
        </View>

        <View style={styles.infoRow}>
          <Ionicons name="people" size={20} color="#231716" />
          <Text style={styles.infoText}>{showcase.participants} teams</Text>
        </View>

        <View style={styles.infoRow}>
          <MaterialCommunityIcons
            name="trophy-variant"
            size={20}
            color="#231716"
          />
          <Text
            style={[
              styles.infoText,
              { color: getStatusColor(showcase.status) },
            ]}
          >
            {showcase.status}
          </Text>
        </View>
      </View>

      <View style={styles.infoSection}>
        <Text style={styles.sectionTitle}>About</Text>
        <Text style={styles.description}>{showcase.description}</Text>
      </View>

      <View style={styles.infoSection}>
        <Text style={styles.sectionTitle}>Categories</Text>
        <View style={styles.tagsContainer}>
          {showcase.tags.map((tag, index) => (
            <View key={index} style={styles.tag}>
              <Text style={styles.tagText}>{tag}</Text>
            </View>
          ))}
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  tabContainer: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  showcaseImage: {
    height: 200,
    borderRadius: 7,
    marginHorizontal: 16,
    marginVertical: 16,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  infoSection: {
    backgroundColor: "#fff",
    padding: 20,
    marginBottom: 16,
    borderRadius: 7,
    marginHorizontal: 16,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#231716",
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  infoText: {
    fontSize: 16,
    color: "#231716",
    marginLeft: 12,
  },
  description: {
    fontSize: 16,
    color: "#231716",
    lineHeight: 24,
  },
  tagsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  tag: {
    backgroundColor: "#231716",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  tagText: {
    fontSize: 14,
    color: "#fff",
    fontWeight: "500",
  },
});
