import React from "react";
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  Modal,
  TouchableOpacity,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useFonts, BakbakOne_400Regular } from "@expo-google-fonts/bakbak-one";
import OverviewTab from "./OverviewTab";
import { ShowcaseDetails } from "../../../../data/showcases/showcases";

interface ShowcaseOverviewModalProps {
  visible: boolean;
  showcase: ShowcaseDetails;
  onClose: () => void;
}

export default function showcaseOverviewModal({
  visible,
  showcase,
  onClose,
}: ShowcaseOverviewModalProps) {
  const [fontsLoaded] = useFonts({
    BakbakOne_400Regular,
  });

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>howcase Overview</Text>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <View style={styles.roundXButton}>
              <Ionicons name="close" size={17} color="#000" />
            </View>
          </TouchableOpacity>
        </View>
        <OverviewTab showcase={showcase} />
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: "#fff",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  modalTitle: {
    fontSize: 20,
    color: "#231716",
    fontFamily: "BakbakOne_400Regular",
  },
  closeButton: {
    padding: 4,
  },
  roundXButton: {
    width: 25,
    height: 25,
    borderRadius: 14,
    backgroundColor: "#EEEEEE",
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#E0E0E0",
  },
});
