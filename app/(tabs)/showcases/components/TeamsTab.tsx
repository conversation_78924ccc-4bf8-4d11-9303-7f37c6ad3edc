import React from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";

export default function TeamsTab() {
  const teams = [
    { name: "Hong Kong Dragons", country: "Hong Kong", players: 18 },
    { name: "Seoul Warriors", country: "Korea", players: 20 },
    { name: "Tokyo Ice", country: "Japan", players: 19 },
    { name: "Beijing Blades", country: "China", players: 17 },
  ];

  return (
    <ScrollView style={styles.tabContainer}>
      {teams.map((team, index) => (
        <TouchableOpacity key={index} style={styles.teamCard}>
          <View style={styles.teamInfo}>
            <Text style={styles.teamName}>{team.name}</Text>
            <Text style={styles.teamCountry}>{team.country}</Text>
          </View>
          <Text style={styles.teamPlayers}>{team.players} players</Text>
          <Ionicons name="chevron-forward" size={20} color="#8E8E93" />
        </TouchableOpacity>
      ))}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  tabContainer: {
    flex: 1,
    backgroundColor: "#F0F1F5",
    paddingTop: 15,
  },
  teamCard: {
    backgroundColor: "#fff",
    marginHorizontal: 10,
    marginBottom: 12,
    padding: 16,
    borderRadius: 8,
    flexDirection: "row",
    alignItems: "center",
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 1,
    shadowColor: "#000",
    borderWidth: 0.2,
    borderColor: "#CACDD4",
  },
  teamInfo: {
    flex: 1,
  },
  teamName: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
  },
  teamCountry: {
    fontSize: 14,
    color: "#666",
    marginTop: 4,
  },
  teamPlayers: {
    fontSize: 14,
    color: "#666",
    marginRight: 8,
  },
});
