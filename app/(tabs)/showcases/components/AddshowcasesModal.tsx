import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  Modal,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Alert,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { addShowcase, updateShowcase, ShowcaseDetails } from '../../../../data/showcases/showcases';

interface AddShowcaseModalProps {
  visible: boolean;
  onClose: () => void;
  editingShowcase?: ShowcaseDetails | null;
}

interface ShowcaseFormData {
  title: string;
  subtitle: string;
  date: string;
  tags: string[];
  image: string;
  country: string;
  location: string;
  participants: number;
  status: string;
  description: string;
}

export default function AddShowcaseModal({ visible, onClose, editingShowcase }: AddShowcaseModalProps) {
  const [isCreating, setIsCreating] = useState(false);
  const [formData, setFormData] = useState<ShowcaseFormData>({
    title: '',
    subtitle: '',
    date: '',
    tags: [],
    image: 'https://picsum.photos/400/300?random=1',
    country: '',
    location: '',
    participants: 0,
    status: 'Upcoming',
    description: '',
  });

  const [tagInput, setTagInput] = useState('');

  const [showDatePicker, setShowDatePicker] = useState<{
    show: boolean;
    field: 'date';
  }>({ show: false, field: 'date' });

  const [selectedDate, setSelectedDate] = useState({
    day: new Date().getDate(),
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear(),
  });

  // Track if date picker has been opened for this form session
  const [datePickerInitialized, setDatePickerInitialized] = useState(false);

  // Add refs for scroll views to control scroll position
  const dayScrollRef = useRef<ScrollView>(null);
  const monthScrollRef = useRef<ScrollView>(null);
  const yearScrollRef = useRef<ScrollView>(null);

  // Generate arrays for picker options
  const days = Array.from({ length: 31 }, (_, i) => i + 1);
  const months = [
    { value: 1, label: 'January' },
    { value: 2, label: 'February' },
    { value: 3, label: 'March' },
    { value: 4, label: 'April' },
    { value: 5, label: 'May' },
    { value: 6, label: 'June' },
    { value: 7, label: 'July' },
    { value: 8, label: 'August' },
    { value: 9, label: 'September' },
    { value: 10, label: 'October' },
    { value: 11, label: 'November' },
    { value: 12, label: 'December' },
  ];
  const years = Array.from({ length: 10 }, (_, i) => new Date().getFullYear() + i);

  // Reset form when modal opens or when editing Showcase changes
  useEffect(() => {
    if (visible) {
      // Reset date picker initialization when modal opens
      setDatePickerInitialized(false);
      
      if (editingShowcase) {
        // Load existing Showcase data
        setFormData({
          title: editingShowcase.title || '',
          subtitle: editingShowcase.subtitle || '',
          date: editingShowcase.date || '',
          tags: editingShowcase.tags || [],
          image: editingShowcase.image || 'https://picsum.photos/400/300?random=1',
          country: editingShowcase.country || '',
          location: editingShowcase.location || '',
          participants: editingShowcase.participants || 0,
          status: editingShowcase.status || 'Upcoming',
          description: editingShowcase.description || '',
        });
      } else {
        // Reset for new Showcase
        setFormData({
          title: '',
          subtitle: '',
          date: '',
          tags: [],
          image: 'https://picsum.photos/400/300?random=1',
          country: '',
          location: '',
          participants: 0,
          status: 'Upcoming',
          description: '',
        });
      }
      setTagInput('');
    }
  }, [visible, editingShowcase]);

  const handleDatePickerOpen = () => {
    // Only reset to today's date if this is the first time opening the date picker
    if (!datePickerInitialized) {
      const today = new Date();
      setSelectedDate({
        day: today.getDate(),
        month: today.getMonth() + 1,
        year: today.getFullYear(),
      });
      setDatePickerInitialized(true);
    }
    setShowDatePicker({ show: true, field: 'date' });

    // Scroll to selected items after a short delay to ensure the modal is rendered
    setTimeout(() => {
      scrollToSelectedItems();
    }, 100);
  };

  const scrollToSelectedItems = () => {
    // Calculate scroll positions for each picker
    const dayIndex = selectedDate.day - 1;
    const monthIndex = selectedDate.month - 1;
    const yearIndex = years.findIndex(year => year === selectedDate.year);

    const itemHeight = 50; // Approximate item height including margins

    // Scroll to selected items
    dayScrollRef.current?.scrollTo({ y: dayIndex * itemHeight, animated: true });
    monthScrollRef.current?.scrollTo({ y: monthIndex * itemHeight, animated: true });
    yearScrollRef.current?.scrollTo({ y: yearIndex * itemHeight, animated: true });
  };

  const handleDateConfirm = () => {
    const newDate = new Date(selectedDate.year, selectedDate.month - 1, selectedDate.day);
    
    // Validate date
    if (newDate.getDate() !== selectedDate.day || newDate.getMonth() !== selectedDate.month - 1) {
      Alert.alert('Error', 'Invalid date selected');
      return;
    }

    const formattedDate = newDate.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    setFormData(prev => ({ ...prev, date: formattedDate }));
    setShowDatePicker({ show: false, field: 'date' });
  };

  const handleCloseDatePicker = () => {
    setShowDatePicker({ show: false, field: 'date' });
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }));
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const ScrollableDatePicker = () => (
    <Modal
      visible={showDatePicker.show}
      transparent
      animationType="slide"
      onRequestClose={handleCloseDatePicker}
    >
      <View style={styles.datePickerOverlay}>
        <View style={styles.datePickerContainer}>
          <Text style={styles.datePickerTitle}>Select Showcase Date</Text>
          
          <View style={styles.datePickerContent}>
            {/* Day Picker */}
            <View style={styles.pickerColumn}>
              <Text style={styles.pickerLabel}>Day</Text>
              <ScrollView 
                ref={dayScrollRef}
                style={styles.pickerScroll}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.pickerScrollContent}
              >
                {days.map((day) => (
                  <TouchableOpacity
                    key={day}
                    style={[
                      styles.pickerItem,
                      selectedDate.day === day && styles.pickerItemSelected
                    ]}
                    onPress={() => setSelectedDate(prev => ({ ...prev, day }))
                    }
                  >
                    <Text style={[
                      styles.pickerItemText,
                      selectedDate.day === day && styles.pickerItemTextSelected
                    ]}>
                      {day}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            {/* Month Picker */}
            <View style={styles.pickerColumn}>
              <Text style={styles.pickerLabel}>Month</Text>
              <ScrollView 
                ref={monthScrollRef}
                style={styles.pickerScroll}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.pickerScrollContent}
              >
                {months.map((month) => (
                  <TouchableOpacity
                    key={month.value}
                    style={[
                      styles.pickerItem,
                      selectedDate.month === month.value && styles.pickerItemSelected
                    ]}
                    onPress={() => setSelectedDate(prev => ({ ...prev, month: month.value }))}
                  >
                    <Text style={[
                      styles.pickerItemText,
                      selectedDate.month === month.value && styles.pickerItemTextSelected
                    ]}>
                      {month.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            {/* Year Picker */}
            <View style={styles.pickerColumn}>
              <Text style={styles.pickerLabel}>Year</Text>
              <ScrollView 
                ref={yearScrollRef}
                style={styles.pickerScroll}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.pickerScrollContent}
              >
                {years.map((year) => (
                  <TouchableOpacity
                    key={year}
                    style={[
                      styles.pickerItem,
                      selectedDate.year === year && styles.pickerItemSelected
                    ]}
                    onPress={() => setSelectedDate(prev => ({ ...prev, year }))}
                  >
                    <Text style={[
                      styles.pickerItemText,
                      selectedDate.year === year && styles.pickerItemTextSelected
                    ]}>
                      {year}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          </View>
          
          <View style={styles.datePickerButtons}>
            <TouchableOpacity
              style={[styles.datePickerButton, styles.datePickerCancel]}
              onPress={handleCloseDatePicker}
            >
              <Text style={styles.datePickerCancelText}>Cancel</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.datePickerButton, styles.datePickerConfirm]}
              onPress={handleDateConfirm}
            >
              <Text style={styles.datePickerConfirmText}>Confirm</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  const handleSubmit = async () => {
    console.log('🔍 Submit button pressed');
    console.log('🔍 Form data:', formData);
    console.log('🔍 Editing Showcase:', editingShowcase);
    
    if (!formData.title || !formData.subtitle || !formData.location || !formData.country) {
      Alert.alert('Error', 'Please fill in all required fields (Title, Subtitle, Location, Country)');
      return;
    }

    setIsCreating(true);
    try {
      if (editingShowcase) {
        console.log('🔍 Updating Showcase...');
        await updateShowcase(editingShowcase.id, formData);
        Alert.alert('Success', 'Showcase updated successfully!');
      } else {
        console.log('🔍 Creating Showcase...');
        await addShowcase(formData);
        Alert.alert('Success', 'Showcase created successfully!');
      }
      onClose();
    } catch (error: any) {
      console.error('🔍 Error saving Showcase:', error);
      Alert.alert('Error', error.message || 'Failed to save Showcase');
    } finally {
      setIsCreating(false);
    }
  };

  const isEditing = !!editingShowcase;

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.headerButton}>
            <Ionicons name="close" size={24} color="#000" />
          </TouchableOpacity>
          <Text style={styles.title}>
            {isEditing ? 'Edit Showcase' : 'Add Showcase'}
          </Text>
          <TouchableOpacity 
            onPress={handleSubmit} 
            disabled={isCreating}
            style={[styles.headerButton, styles.saveButtonContainer]}
          >
            {isCreating ? (
              <ActivityIndicator size="small" color="#D62828" />
            ) : (
              <Text style={styles.saveButton}>
                {isEditing ? 'Update' : 'Save'}
              </Text>
            )}
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.form}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Showcase Title *</Text>
            <TextInput
              style={styles.input}
              value={formData.title}
              onChangeText={(text) => setFormData(prev => ({ ...prev, title: text }))}
              placeholder="Enter Showcase title"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Subtitle *</Text>
            <TextInput
              style={styles.input}
              value={formData.subtitle}
              onChangeText={(text) => setFormData(prev => ({ ...prev, subtitle: text }))}
              placeholder="Enter Showcase subtitle"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Description</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={formData.description}
              onChangeText={(text) => setFormData(prev => ({ ...prev, description: text }))}
              placeholder="Enter Showcase description"
              multiline
              numberOfLines={4}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Date *</Text>
            <TouchableOpacity 
              style={styles.dateInput}
              onPress={handleDatePickerOpen}
            >
              <Text style={styles.dateText}>
                {formData.date || 'Select Showcase date'}
              </Text>
              <Ionicons name="calendar" size={20} color="#666" />
            </TouchableOpacity>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Location *</Text>
            <TextInput
              style={styles.input}
              value={formData.location}
              onChangeText={(text) => setFormData(prev => ({ ...prev, location: text }))}
              placeholder="Enter Showcase location"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Country *</Text>
            <TextInput
              style={styles.input}
              value={formData.country}
              onChangeText={(text) => setFormData(prev => ({ ...prev, country: text }))}
              placeholder="Enter country"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Status</Text>
            <TextInput
              style={styles.input}
              value={formData.status}
              onChangeText={(text) => setFormData(prev => ({ ...prev, status: text }))}
              placeholder="Showcase status"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Expected Participants</Text>
            <TextInput
              style={styles.input}
              value={formData.participants.toString()}
              onChangeText={(text) => setFormData(prev => ({ ...prev, participants: parseInt(text) || 0 }))}
              placeholder="0"
              keyboardType="numeric"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Image URL</Text>
            <TextInput
              style={styles.input}
              value={formData.image}
              onChangeText={(text) => setFormData(prev => ({ ...prev, image: text }))}
              placeholder="Enter image URL"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Tags</Text>
            <View style={styles.tagInputContainer}>
              <TextInput
                style={[styles.input, styles.tagInput]}
                value={tagInput}
                onChangeText={setTagInput}
                placeholder="Add a tag"
                onSubmitEditing={handleAddTag}
              />
              <TouchableOpacity
                style={styles.addTagButton}
                onPress={handleAddTag}
              >
                <Ionicons name="add" size={20} color="#fff" />
              </TouchableOpacity>
            </View>
            
            {formData.tags.length > 0 && (
              <View style={styles.tagsContainer}>
                {formData.tags.map((tag, index) => (
                  <View key={index} style={styles.tag}>
                    <Text style={styles.tagText}>{tag}</Text>
                    <TouchableOpacity
                      style={styles.removeTagButton}
                      onPress={() => handleRemoveTag(tag)}
                    >
                      <Ionicons name="close" size={16} color="#fff" />
                    </TouchableOpacity>
                  </View>
                ))}
              </View>
            )}
          </View>

          <View style={styles.bottomPadding} />
        </ScrollView>
      </View>

      <ScrollableDatePicker />
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F7F7',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E7',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  headerButton: {
    padding: 8,
    minWidth: 40,
    minHeight: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  saveButtonContainer: {
    backgroundColor: 'transparent',
  },
  saveButton: {
    color: '#D62828',
    fontSize: 16,
    fontWeight: '600',
  },
  form: {
    flex: 1,
    padding: 16,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#E5E5E7',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  dateInput: {
    borderWidth: 1,
    borderColor: '#E5E5E7',
    borderRadius: 8,
    padding: 12,
    backgroundColor: '#fff',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 16,
    color: '#333',
  },
  datePickerOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  datePickerContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    width: '90%',
    maxWidth: 400,
    maxHeight: '80%',
    padding: 20,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  datePickerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  datePickerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    height: 200,
    marginBottom: 20,
  },
  pickerColumn: {
    flex: 1,
    marginHorizontal: 5,
  },
  pickerLabel: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 10,
    color: '#333',
  },
  pickerScroll: {
    flex: 1,
    backgroundColor: '#F9F9F9',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E5E7',
  },
  pickerScrollContent: {
    paddingVertical: 5,
  },
  pickerItem: {
    paddingVertical: 12,
    paddingHorizontal: 8,
    alignItems: 'center',
    borderRadius: 6,
    marginHorizontal: 4,
    marginVertical: 2,
  },
  pickerItemSelected: {
    backgroundColor: '#D62828',
  },
  pickerItemText: {
    fontSize: 16,
    color: '#333',
  },
  pickerItemTextSelected: {
    color: '#fff',
    fontWeight: '600',
  },
  datePickerButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 10,
  },
  datePickerButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    minHeight: 44,
    justifyContent: 'center',
  },
  datePickerCancel: {
    backgroundColor: '#F5F5F5',
    borderWidth: 1,
    borderColor: '#E5E5E7',
  },
  datePickerConfirm: {
    backgroundColor: '#D62828',
  },
  datePickerCancelText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '600',
  },
  datePickerConfirmText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  bottomPadding: {
    height: 50,
  },
  tagInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  tagInput: {
    flex: 1,
    marginBottom: 0,
  },
  addTagButton: {
    backgroundColor: '#D62828',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 44,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 8,
  },
  tag: {
    backgroundColor: '#231716',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  tagText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '500',
  },
  removeTagButton: {
    padding: 2,
  },
});
