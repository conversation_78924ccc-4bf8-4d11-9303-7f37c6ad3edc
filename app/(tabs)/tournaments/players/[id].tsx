import React from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  SafeAreaView,
} from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { getPlayerById } from "../../../../data/tournaments/statistics";
import { PlayerWithStatistics } from "../../../../types";

export default function PlayerDetailsScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const player = getPlayerById(id as string);

  if (!player) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={24} color="#231716" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Player Not Found</Text>
        </View>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Player not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  const renderStatRow = (label: string, value: string | number) => (
    <View style={styles.statRow}>
      <Text style={styles.statLabel}>{label}</Text>
      <Text style={styles.statValue}>{value}</Text>
    </View>
  );

  const isGoalie = player.position === "Goalie";

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="arrow-back" size={24} color="#231716" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Player Statistics</Text>
      </View>

      <ScrollView style={styles.content}>
        {/* Player Info Section */}
        <View style={styles.playerInfoSection}>
          <Image
            style={styles.playerImage}
            source={{ uri: player.avatar }}
            resizeMode="cover"
          />
          <View style={styles.playerBasicInfo}>
            <Text style={styles.playerName}>{player.name}</Text>
            <Text style={styles.playerPosition}>{player.position}</Text>
            <Text style={styles.playerNumber}>#{player.jerseyNumber}</Text>
          </View>
        </View>

        {/* Player Details */}
        <View style={styles.detailsSection}>
          <Text style={styles.sectionTitle}>Player Details</Text>
          <View style={styles.detailsCard}>
            {player.height && renderStatRow("Height", player.height)}
            {player.weight && renderStatRow("Weight", player.weight)}
            {player.birthDate && renderStatRow("Birth Date", player.birthDate)}
            {player.nationality && renderStatRow("Nationality", player.nationality)}
          </View>
        </View>

        {/* Statistics Section */}
        <View style={styles.statisticsSection}>
          <Text style={styles.sectionTitle}>Tournament Statistics</Text>
          <View style={styles.statsCard}>
            {renderStatRow("Games Played", player.statistics.gamesPlayed)}
            {renderStatRow("Goals", player.statistics.goals)}
            {renderStatRow("Assists", player.statistics.assists)}
            {renderStatRow("Points", player.statistics.points)}
            {!isGoalie && renderStatRow("+/-", player.statistics.plusMinus)}
            {renderStatRow("Penalty Minutes", player.statistics.penaltyMinutes)}
            
            {!isGoalie && (
              <>
                {renderStatRow("Shots", player.statistics.shots)}
                {renderStatRow("Shooting %", `${player.statistics.shootingPercentage.toFixed(1)}%`)}
                {renderStatRow("Power Play Goals", player.statistics.powerPlayGoals)}
                {renderStatRow("Short Handed Goals", player.statistics.shortHandedGoals)}
                {renderStatRow("Game Winning Goals", player.statistics.gameWinningGoals)}
                {renderStatRow("Time on Ice", `${player.statistics.timeOnIce.toFixed(1)} min`)}
                {renderStatRow("Hits", player.statistics.hits)}
                {renderStatRow("Blocked Shots", player.statistics.blockedShots)}
                {player.statistics.faceoffWins > 0 && (
                  <>
                    {renderStatRow("Faceoff Wins", player.statistics.faceoffWins)}
                    {renderStatRow("Faceoff %", `${player.statistics.faceoffPercentage.toFixed(1)}%`)}
                  </>
                )}
              </>
            )}

            {isGoalie && player.statistics.saves && (
              <>
                {renderStatRow("Saves", player.statistics.saves)}
                {renderStatRow("Shots Against", player.statistics.shotsAgainst || 0)}
                {renderStatRow("Save %", `${player.statistics.savePercentage?.toFixed(1)}%`)}
                {renderStatRow("Goals Against", player.statistics.goalsAgainst || 0)}
                {renderStatRow("GAA", player.statistics.goalsAgainstAverage?.toFixed(2) || "0.00")}
                {renderStatRow("Shutouts", player.statistics.shutouts || 0)}
                {renderStatRow("Wins", player.statistics.wins || 0)}
                {renderStatRow("Losses", player.statistics.losses || 0)}
              </>
            )}
          </View>
        </View>

        {/* Description Section */}
        {player.description && (
          <View style={styles.descriptionSection}>
            <Text style={styles.sectionTitle}>About</Text>
            <View style={styles.descriptionCard}>
              <Text style={styles.descriptionText}>{player.description}</Text>
            </View>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: "#fff",
    borderBottomWidth: 0.5,
    borderBottomColor: "#A8A9AB",
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#231716",
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  errorText: {
    fontSize: 16,
    color: "#666",
  },
  playerInfoSection: {
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 20,
    marginTop: 16,
    flexDirection: "row",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 1,
  },
  playerImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginRight: 16,
  },
  playerBasicInfo: {
    flex: 1,
  },
  playerName: {
    fontSize: 24,
    fontWeight: "700",
    color: "#231716",
    marginBottom: 4,
  },
  playerPosition: {
    fontSize: 16,
    color: "#7f7271",
    marginBottom: 4,
  },
  playerNumber: {
    fontSize: 20,
    fontWeight: "600",
    color: "#D52B1E",
  },
  detailsSection: {
    marginTop: 16,
  },
  statisticsSection: {
    marginTop: 16,
  },
  descriptionSection: {
    marginTop: 16,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#231716",
    marginBottom: 8,
  },
  detailsCard: {
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 1,
  },
  statsCard: {
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 1,
  },
  descriptionCard: {
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 1,
  },
  statRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
    borderBottomWidth: 0.5,
    borderBottomColor: "#E5E5E5",
  },
  statLabel: {
    fontSize: 16,
    color: "#666",
  },
  statValue: {
    fontSize: 16,
    fontWeight: "600",
    color: "#231716",
  },
  descriptionText: {
    fontSize: 16,
    lineHeight: 24,
    color: "#333",
  },
});
