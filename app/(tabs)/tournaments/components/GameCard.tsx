import React from "react";
import { View, Text, StyleSheet } from "react-native";

function TeamLogoOrShield({ logo }: { logo?: string }) {
  // You can use your existing implementation or a placeholder
  return <View style={styles.teamLogoPlaceholder} />;
}

interface GameCardProps {
  game: {
    date: string;
    teams: Array<{
      name: string;
      fullName: string;
      logo: string;
      score: number;
      periodScores: number[];
    }>;
  };
}

export default function GameCard({ game }: GameCardProps) {
  if (!game.teams || game.teams.length < 2) {
    return (
      <View style={styles.card}>
        <Text style={styles.gameDate}>Game data unavailable</Text>
      </View>
    );
  }

  // Defensive: fallback to empty object if team is missing
  const teamA = game.teams[0] || { name: "?", fullName: "?", logo: "", score: "-", periodScores: [] };
  const teamB = game.teams[1] || { name: "?", fullName: "?", logo: "", score: "-", periodScores: [] };

  return (
    <View style={styles.card}>
      <Text style={styles.gameDate}>{game.date}</Text>
      <View style={styles.frameParent}>
        <View style={styles.teamContainer}>
          <TeamLogoOrShield logo={teamA.logo} />
          <Text
            style={styles.scoreTeamName}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {teamA.name}
          </Text>
        </View>
        <View style={styles.scoreGroup}>
          <Text style={styles.scoreText}>{teamA.score}</Text>
          <Text style={styles.scoreDash}>-</Text>
          <Text style={styles.scoreText}>{teamB.score}</Text>
        </View>
        <View style={styles.teamContainer}>
          <TeamLogoOrShield logo={teamB.logo} />
          <Text
            style={styles.scoreTeamName}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {teamB.name}
          </Text>
        </View>
      </View>
      <View style={styles.divider} />
      {/* Period Table */}
      <View style={styles.periodTable}>
        <View style={styles.periodTableContainer}>
          <View style={styles.periodTeamColumn}>
            <Text style={styles.periodHeaderText}>Team</Text>
            <Text style={styles.teamNameText}>{teamA.fullName}</Text>
            <Text style={styles.teamNameText}>{teamB.fullName}</Text>
          </View>
          <View style={styles.periodColumnsContainer}>
            {/* Periods */}
            {[0, 1, 2, 3].map((p) => (
              <View style={styles.periodColumn} key={p}>
                <Text style={styles.periodHeaderText}>{p === 3 ? "OT" : p + 1}</Text>
                <Text style={styles.periodScoreText}>{teamA.periodScores[p] ?? "-"}</Text>
                <Text style={styles.periodScoreText}>{teamB.periodScores[p] ?? "-"}</Text>
              </View>
            ))}
            {/* Total */}
            <View style={styles.periodColumn}>
              <Text style={styles.periodHeaderText}>T</Text>
              <Text style={styles.periodScoreText}>{teamA.score}</Text>
              <Text style={styles.periodScoreText}>{teamB.score}</Text>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    marginHorizontal: 10,
    marginBottom: 12,
    padding: 16,
    shadowColor: "#000000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.07,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 0.2,
    borderColor: "#CACDD4",
  },
  gameDate: {
    fontSize: 16,
    color: "#7F7271",
    textAlign: "center",
    marginBottom: 8,
  },
  frameParent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 12,
    display: "flex",
  },
  teamContainer: {
    gap: 5,
    justifyContent: "center",
    alignItems: "center",
    marginHorizontal: 16,
    flex: 1,
  },
  teamLogoPlaceholder: {
    width: 45,
    height: 45,
    backgroundColor: "#E0E0E0",
    borderRadius: 22,
  },
  scoreTeamName: {
    fontSize: 15, // smaller font size
    fontWeight: "400",
    color: "#231716",
    textAlign: "center",
    width: 80, // restrict width to help prevent wrapping
  },
  scoreGroup: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    minWidth: 100, // ensures enough space for centering
    flex: 1,
  },
  scoreText: {
    fontSize: 30,
    color: "#231716",
    textAlign: "center",
  },
  scoreDash: {
    width: 14,
    fontSize: 30,
    color: "#231716",
    textAlign: "center",
    justifyContent: "center",
    alignItems: "center",
  },
  divider: {
    height: 1,
    backgroundColor: "#E0E0E0",
    marginVertical: 8,
  },
  periodTable: {
    width: "100%",
    paddingHorizontal: 10,
    paddingVertical: 0,
  },
  periodTableContainer: {
    gap: 0,
    justifyContent: "space-between",
    flexDirection: "row",
    alignItems: "center",
  },
  periodTeamColumn: {
    gap: 10,
    justifyContent: "center",
  },
  periodColumnsContainer: {
    gap: 20,
    flexDirection: "row",
    alignItems: "center",
  },
  periodColumn: {
    gap: 10,
    justifyContent: "center",
    alignItems: "center",
  },
  periodHeaderText: {
    color: "#7f7271",
    textAlign: "left",
    fontSize: 16,
  },
  teamNameText: {
    color: "#231716",
    textAlign: "left",
    fontSize: 16,
  },
  periodScoreText: {
    color: "#231716",
    textAlign: "left",
    fontSize: 16,
  },
});