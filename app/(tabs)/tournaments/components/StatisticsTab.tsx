import React from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
} from "react-native";
import { useRouter } from "expo-router";
import { mockPlayersWithStatistics } from "../../../../data/tournaments/statistics";
import {
  useFonts,
  RacingSansOne_400Regular,
} from "@expo-google-fonts/racing-sans-one";

export default function StatisticsTab() {
  const router = useRouter();
  const [fontsLoaded] = useFonts({
    RacingSansOne_400Regular,
  });

  const handlePlayerPress = (playerId: string) => {
    router.push(`/tournaments/players/${playerId}`);
  };

  if (!fontsLoaded) {
    return <Text>Loading...</Text>;
  }

  return (
    <ScrollView style={styles.tabContainer}>
      {mockPlayersWithStatistics.map((player) => (
        <TouchableOpacity
          key={player.id}
          style={styles.playerCard}
          onPress={() => handlePlayerPress(player.id)}
          activeOpacity={0.8}
        >
          <View style={styles.playerInfo}>
            <Image
              style={styles.playerAvatar}
              source={{ uri: player.avatar }}
              resizeMode="cover"
            />
            <View style={styles.playerDetails}>
              <Text style={styles.playerName}>{player.name}</Text>
              <Text style={styles.playerPosition}>{player.position}</Text>
            </View>
          </View>
          <Text
            style={[
              styles.playerNumber,
              { fontFamily: "RacingSansOne_400Regular" },
            ]}
          >
            {player.jerseyNumber.toString().padStart(2, "0")}
          </Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  tabContainer: {
    flex: 1,
    backgroundColor: "#F0F1F5",
    paddingTop: 15,
    paddingHorizontal: 16,
  },
  playerCard: {
    backgroundColor: "#fff",
    marginBottom: 10,
    padding: 10,
    borderRadius: 5,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    shadowColor: "rgba(0, 0, 0, 0.05)",
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 1,
    shadowRadius: 8,
    elevation: 8,
  },
  playerInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  playerAvatar: {
    width: 60,
    height: 60,
    borderRadius: 4,
    overflow: "hidden",
  },
  playerDetails: {
    gap: 5,
    justifyContent: "center",
  },
  playerName: {
    fontSize: 18,
    lineHeight: 19,
    fontWeight: "700",
    color: "#231716",
    textAlign: "left",
  },
  playerPosition: {
    fontSize: 16,
    lineHeight: 17,
    color: "#7f7271",
    textAlign: "left",
  },
  playerNumber: {
    fontSize: 33,
    lineHeight: 38,
    fontWeight: "400",
    paddingRight: 5,
    color: "#231716",
    textAlign: "left",
    fontFamily: "RacingSansOne-Regular",
  },
});
