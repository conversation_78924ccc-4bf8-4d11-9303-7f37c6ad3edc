import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
} from "react-native";
import { useRouter } from "expo-router";
import GameCard from "./GameCard";

interface Team {
  name: string;
  fullName: string;
  logo: string;
  score: number;
  periodScores: number[];
}
interface Game {
  gameNumber: number;
  date: string;
  teams: Team[];
}

const games: Game[] = [
  {
    gameNumber: 1,
    date: "Tue, 3 Jun 2025",
    teams: [
      {
        name: "Panthers",
        fullName: "Florida Panthers",
        logo: "panthers_logo.png",
        score: 4,
        periodScores: [1, 1, 1, 1],
      },
      {
        name: "Oilers",
        fullName: "Edmonton Oilers",
        logo: "oilers_logo.png",
        score: 3,
        periodScores: [2, 1, 0, 0],
      },
    ],
  },
  {
    gameNumber: 2,
    date: "Wed, 7 Jun 2025",
    teams: [
      {
        name: "Columbus",
        fullName: "Columbus Blue Jackets", // <-- add fullName
        logo: "columbus_logo.png",
        score: 3,
        periodScores: [1, 1, 1, 0], // <-- add periodScores
      },
      {
        name: "Oilers",
        fullName: "Edmonton Oilers",
        logo: "oilers_logo.png",
        score: 7,
        periodScores: [2, 2, 2, 1], // <-- add periodScores
      },
    ],
  },
  {
    gameNumber: 3,
    date: "Tue, 10 Jun 2025",
    teams: [
      {
        name: "Oilers",
        fullName: "Edmonton Oilers",
        logo: "oilers_logo.png",
        score: 0, // Use 0 if null
        periodScores: [0, 0, 0, 0], // <-- add periodScores
      },
      {
        name: "Panthers",
        fullName: "Florida Panthers",
        logo: "panthers_logo.png",
        score: 0,
        periodScores: [0, 0, 0, 0], // <-- add periodScores
      },
    ],
  },
];

function ShieldIcon() {
  return (
    <View style={styles.shieldContainer}>
      <View style={styles.shieldShape} />
    </View>
  );
}

function TeamLogoOrShield({ logo }: { logo?: string }) {
  const [error, setError] = useState(false);
  if (!logo || logo.trim() === "" || error) {
    return <ShieldIcon />;
  }
  return (
    <Image
      source={{ uri: logo }}
      style={styles.teamLogo}
      onError={() => setError(true)}
    />
  );
}

function ScheduleCard({ game }: { game: Game }) {
  // Split date into two lines: first line (weekday, day, month), second line (year)
  const [datePart, yearPart] = (() => {
    const parts = game.date.split(", ");
    if (parts.length === 2) {
      // If format is "Tue, 3 Jun 2025"
      const yearMatch = parts[1].match(/\d{4}$/);
      const year = yearMatch ? yearMatch[0] : "";
      return [parts[0] + ", " + parts[1].replace(/\s*\d{4}$/, ""), year];
    }
    // fallback: just split last 4 digits as year
    const match = game.date.match(/(.*?)(\d{4})$/);
    if (match) {
      return [match[1].trim(), match[2]];
    }
    return [game.date, ""];
  })();

  return (
    <View style={styles.card}>
      <Text style={styles.gameNumber}>Game {game.gameNumber}</Text>
      <View style={styles.scoresRow}>
        <View style={styles.teamsScoresContainer}>
          {game.teams.map((team: Team, idx: number) => (
            <View key={team.name} style={styles.teamRow}>
              <View style={styles.teamInfo}>
                <TeamLogoOrShield logo={team.logo} />
                <Text style={styles.teamName}>{team.name}</Text>
              </View>
              {team.score !== null && (
                <Text style={styles.teamScore}>{team.score}</Text>
              )}
            </View>
          ))}
        </View>
        <View style={styles.verticalSeparator} />
        <View style={styles.dateContainer}>
          <Text style={styles.gameDate}>{datePart}</Text>
          <Text style={styles.gameYear}>{yearPart}</Text>
        </View>
      </View>
    </View>
  );
}

export default function ScheduleTab() {
  const router = useRouter();
  return (
    <ScrollView style={styles.tabContainer}>
      {games.map((game) => (
        <TouchableOpacity
          key={game.gameNumber}
          activeOpacity={0.8}
          onPress={() => router.push("/tournaments/gamecenter")}
        >
          <GameCard game={game} />
        </TouchableOpacity>
      ))}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  tabContainer: {
    flex: 1,
    backgroundColor: "#F0F1F5",
    paddingTop: 15,
  },
  card: {
    backgroundColor: "#fff",
    borderRadius: 7,
    marginHorizontal: 10,
    marginBottom: 12,
    paddingVertical: 14,
    paddingHorizontal: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 1,
    display: "flex",
    flexDirection: "column",
    gap: 5,
    borderWidth: 0.2,
    borderColor: "#CACDD4",
  },
  gameNumber: {
    fontSize: 14,
    color: "#7F7271",
    fontWeight: "400",
    marginTop: 5,
    marginBottom: 2,
  },
  scoresRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  teamsScoresContainer: {
    flex: 1,
    flexDirection: "column",
    gap: 6,
  },
  verticalSeparator: {
    width: 1,
    height: 70,
    backgroundColor: "#ab9f9e",
    marginHorizontal: 15,
    alignSelf: "center",
  },
  dateContainer: {
    justifyContent: "center",
    alignItems: "center",
    flexWrap: "wrap",
    flex: 0,
  },
  teamRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 2,
    gap: 10,
  },
  teamInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  teamLogo: {
    width: 32,
    height: 32,
    resizeMode: "contain",
  },
  shieldContainer: {
    width: 32,
    height: 32,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 0,
  },
  shieldShape: {
    width: 26,
    height: 28,
    backgroundColor: "#bdbdbd",
    borderTopLeftRadius: 13,
    borderTopRightRadius: 13,
    borderBottomLeftRadius: 6,
    borderBottomRightRadius: 6,
  },
  teamName: {
    fontWeight: "400",
    fontSize: 20,
    color: "#231716",
  },
  teamScore: {
    fontWeight: "400",
    fontSize: 20,
    color: "#231716",
  },
  gameDate: {
    fontSize: 15,
    color: "#231716",
    textAlign: "right",
    lineHeight: 18,
  },
  gameYear: {
    fontSize: 15,
    color: "#231716",
    textAlign: "right",
    lineHeight: 18,
  },
});
