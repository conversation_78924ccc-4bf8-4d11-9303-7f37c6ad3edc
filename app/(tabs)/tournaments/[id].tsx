import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
} from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { useFonts, BakbakOne_400Regular } from "@expo-google-fonts/bakbak-one";
import TournamentTabs from "./components/TournamentTabs";
import StandingsTab from "./components/StandingsTab";
import GamesTab from "./components/GamesTab";
import TeamsTab from "./components/TeamsTab";
import StatisticsTab from "./components/StatisticsTab";
import TournamentOverviewModal from "./components/TournamentOverviewModal";
import { getTournamentById } from "./../../../data/tournaments/tournaments";

export default function TournamentDetailsScreen() {
  const { id } = useLocalSearchParams();
  const tournament = getTournamentById(id as string);
  const router = useRouter();
  const [selectedTab, setSelectedTab] = useState("Standings");
  const [showOverviewModal, setShowOverviewModal] = useState(false);

  const [fontsLoaded] = useFonts({
    BakbakOne_400Regular,
  });

  const tabs = ["Standings", "Games", "Teams", "Statistics"];

  if (!tournament) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>Tournament not found</Text>
      </View>
    );
  }

  const renderTabContent = () => {
    switch (selectedTab) {
      case "Standings":
        return <StandingsTab />;
      case "Games":
        return <GamesTab />;
      case "Teams":
        return <TeamsTab />;
      case "Statistics":
        return <StatisticsTab />;
      default:
        return <StandingsTab />;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Custom Back Button */}
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => router.back()}
        activeOpacity={0.7}
      >
        <Ionicons name="chevron-back" size={25} color="#D52B1E" />
        <Text style={styles.backButtonText}>All Tournaments</Text>
      </TouchableOpacity>

      {/* Tournament Title */}
      <View style={styles.titleSection}>
        <Text style={styles.tournamentTitle}>{tournament.title}</Text>
        <TouchableOpacity
          style={styles.overviewButton}
          onPress={() => setShowOverviewModal(true)}
        >
          <Ionicons name="help-circle-outline" size={20} color="#666" />
        </TouchableOpacity>
      </View>

      {/* Tabs */}
      <TournamentTabs
        tabs={tabs}
        selectedTab={selectedTab}
        onTabSelect={setSelectedTab}
      />

      {/* Tab Content */}
      {renderTabContent()}

      {/* Overview Modal */}
      <TournamentOverviewModal
        visible={showOverviewModal}
        tournament={tournament}
        onClose={() => setShowOverviewModal(false)}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  errorText: {
    fontSize: 18,
    color: "#666",
    textAlign: "center",
    marginTop: 50,
  },
  backButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingBottom: 5,
    paddingTop: 15,
    paddingHorizontal: 10,
    backgroundColor: "#fff",
  },
  backButtonText: {
    color: "#D52B1E",
    fontSize: 16,
    fontWeight: "500",
  },
  titleSection: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingBottom: 4,
  },
  tournamentTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#222",
    fontFamily: "BakbakOne_400Regular",
  },
  overviewButton: {
    marginLeft: 12,
    marginTop: 2,
  },
});
