import { PlayerWithStatistics, PlayerStatistics } from "../../types";

export const mockPlayersWithStatistics: PlayerWithStatistics[] = [
  {
    id: "1",
    name: "<PERSON>",
    position: "Center",
    jerseyNumber: 9,
    teamId: "hk-dragons",
    avatar: "https://picsum.photos/120/120?random=1",
    description:
      "A dynamic center with exceptional playmaking abilities and leadership qualities. Known for his vision on the ice and ability to create scoring opportunities for teammates.",
    height: "6'1\"",
    weight: "185 lbs",
    birthDate: "2001-03-15",
    nationality: "Hong Kong",
    statistics: {
      playerId: "1",
      tournamentId: "east-west-spring-classic",
      gamesPlayed: 12,
      goals: 8,
      assists: 15,
      points: 23,
      plusMinus: 12,
      shots: 45,
      shootingPercentage: 17.8,
      powerPlayGoals: 3,
      shortHandedGoals: 1,
      gameWinningGoals: 2,
      timeOnIce: 22.5,
      powerPlayTime: 4.2,
      shortHandedTime: 1.8,
      penaltyMinutes: 8,
      penalties: 4,
      hits: 18,
      blockedShots: 12,
      faceoffWins: 145,
      faceoffLosses: 98,
      faceoffPercentage: 59.7,
    },
  },
  {
    id: "2",
    name: "<PERSON>",
    position: "<PERSON> Winger",
    jerseyNumber: 5,
    teamId: "hk-dragons",
    avatar: "https://picsum.photos/120/120?random=2",
    description:
      "A skilled right winger with a powerful shot and strong defensive awareness. Excellent at creating turnovers and converting them into scoring chances.",
    height: "5'11\"",
    weight: "175 lbs",
    birthDate: "2000-08-22",
    nationality: "Hong Kong",
    statistics: {
      playerId: "2",
      tournamentId: "east-west-spring-classic",
      gamesPlayed: 12,
      goals: 12,
      assists: 8,
      points: 20,
      plusMinus: 8,
      shots: 52,
      shootingPercentage: 23.1,
      powerPlayGoals: 4,
      shortHandedGoals: 0,
      gameWinningGoals: 3,
      timeOnIce: 18.7,
      powerPlayTime: 3.5,
      shortHandedTime: 2.1,
      penaltyMinutes: 12,
      penalties: 6,
      hits: 25,
      blockedShots: 8,
      faceoffWins: 0,
      faceoffLosses: 0,
      faceoffPercentage: 0,
    },
  },
  {
    id: "3",
    name: "Michael Lee",
    position: "Defender",
    jerseyNumber: 1,
    teamId: "hk-dragons",
    avatar: "https://picsum.photos/120/120?random=3",
    description:
      "A solid defensive defenseman with excellent positioning and shot-blocking ability. Provides steady presence in his own zone and contributes on the power play.",
    height: "6'2\"",
    weight: "195 lbs",
    birthDate: "1999-11-10",
    nationality: "Hong Kong",
    statistics: {
      playerId: "3",
      tournamentId: "east-west-spring-classic",
      gamesPlayed: 12,
      goals: 2,
      assists: 12,
      points: 14,
      plusMinus: 15,
      shots: 28,
      shootingPercentage: 7.1,
      powerPlayGoals: 1,
      shortHandedGoals: 0,
      gameWinningGoals: 0,
      timeOnIce: 24.8,
      powerPlayTime: 5.2,
      shortHandedTime: 3.8,
      penaltyMinutes: 6,
      penalties: 3,
      hits: 35,
      blockedShots: 42,
      faceoffWins: 0,
      faceoffLosses: 0,
      faceoffPercentage: 0,
    },
  },
  {
    id: "4",
    name: "Kim Min-jun",
    position: "Center",
    jerseyNumber: 17,
    teamId: "seoul-warriors",
    avatar: "https://picsum.photos/120/120?random=4",
    description:
      "An explosive center with incredible speed and agility. Known for his ability to create breakaway opportunities and his clutch performance in crucial moments.",
    height: "5'10\"",
    weight: "170 lbs",
    birthDate: "2001-05-18",
    nationality: "South Korea",
    statistics: {
      playerId: "4",
      tournamentId: "east-west-spring-classic",
      gamesPlayed: 12,
      goals: 10,
      assists: 11,
      points: 21,
      plusMinus: 6,
      shots: 48,
      shootingPercentage: 20.8,
      powerPlayGoals: 2,
      shortHandedGoals: 2,
      gameWinningGoals: 1,
      timeOnIce: 20.3,
      powerPlayTime: 3.8,
      shortHandedTime: 2.5,
      penaltyMinutes: 14,
      penalties: 7,
      hits: 22,
      blockedShots: 6,
      faceoffWins: 132,
      faceoffLosses: 118,
      faceoffPercentage: 52.8,
    },
  },
  {
    id: "5",
    name: "Park Jae-ho",
    position: "Defender",
    jerseyNumber: 3,
    teamId: "seoul-warriors",
    avatar: "https://picsum.photos/120/120?random=5",
    description:
      "A mobile defenseman with excellent puck-moving skills and offensive instincts. Contributes significantly to the team's transition game and power play.",
    height: "6'0\"",
    weight: "180 lbs",
    birthDate: "2000-02-28",
    nationality: "South Korea",
    statistics: {
      playerId: "5",
      tournamentId: "east-west-spring-classic",
      gamesPlayed: 12,
      goals: 4,
      assists: 10,
      points: 14,
      plusMinus: 9,
      shots: 35,
      shootingPercentage: 11.4,
      powerPlayGoals: 2,
      shortHandedGoals: 0,
      gameWinningGoals: 1,
      timeOnIce: 23.2,
      powerPlayTime: 4.8,
      shortHandedTime: 2.9,
      penaltyMinutes: 10,
      penalties: 5,
      hits: 28,
      blockedShots: 38,
      faceoffWins: 0,
      faceoffLosses: 0,
      faceoffPercentage: 0,
    },
  },
  {
    id: "6",
    name: "Lee Sung-min",
    position: "Goalie",
    jerseyNumber: 30,
    teamId: "seoul-warriors",
    avatar: "https://picsum.photos/120/120?random=6",
    description:
      "A reliable goaltender with excellent reflexes and positioning. Known for his calm demeanor under pressure and ability to make crucial saves in tight games.",
    height: "6'3\"",
    weight: "190 lbs",
    birthDate: "1999-09-12",
    nationality: "South Korea",
    statistics: {
      playerId: "6",
      tournamentId: "east-west-spring-classic",
      gamesPlayed: 8,
      goals: 0,
      assists: 1,
      points: 1,
      plusMinus: 0,
      shots: 0,
      shootingPercentage: 0,
      powerPlayGoals: 0,
      shortHandedGoals: 0,
      gameWinningGoals: 0,
      timeOnIce: 60.0,
      powerPlayTime: 0,
      shortHandedTime: 0,
      penaltyMinutes: 2,
      penalties: 1,
      hits: 0,
      blockedShots: 0,
      faceoffWins: 0,
      faceoffLosses: 0,
      faceoffPercentage: 0,
      // Goalie-specific stats
      saves: 245,
      shotsAgainst: 268,
      savePercentage: 91.4,
      goalsAgainst: 23,
      goalsAgainstAverage: 2.88,
      shutouts: 1,
      wins: 6,
      losses: 2,
      overtimeLosses: 0,
    },
  },
  {
    id: "7",
    name: "Yuki Tanaka",
    position: "Right Winger",
    jerseyNumber: 11,
    teamId: "tokyo-ice",
    avatar: "https://picsum.photos/120/120?random=7",
    description:
      "A versatile forward with excellent hockey IQ and two-way play. Contributes both offensively and defensively, making him valuable in all situations.",
    height: "5'9\"",
    weight: "165 lbs",
    birthDate: "2001-07-03",
    nationality: "Japan",
    statistics: {
      playerId: "7",
      tournamentId: "east-west-spring-classic",
      gamesPlayed: 12,
      goals: 6,
      assists: 14,
      points: 20,
      plusMinus: 4,
      shots: 38,
      shootingPercentage: 15.8,
      powerPlayGoals: 1,
      shortHandedGoals: 1,
      gameWinningGoals: 1,
      timeOnIce: 17.8,
      powerPlayTime: 2.9,
      shortHandedTime: 3.2,
      penaltyMinutes: 6,
      penalties: 3,
      hits: 15,
      blockedShots: 14,
      faceoffWins: 0,
      faceoffLosses: 0,
      faceoffPercentage: 0,
    },
  },
  {
    id: "8",
    name: "Hiroshi Sato",
    position: "Defender",
    jerseyNumber: 4,
    teamId: "tokyo-ice",
    avatar: "https://picsum.photos/120/120?random=8",
    description:
      "A physical defenseman who excels at shutting down opposing forwards. Known for his strong body checking and ability to clear the front of the net.",
    height: "6'1\"",
    weight: "200 lbs",
    birthDate: "2000-01-25",
    nationality: "Japan",
    statistics: {
      playerId: "8",
      tournamentId: "east-west-spring-classic",
      gamesPlayed: 12,
      goals: 1,
      assists: 8,
      points: 9,
      plusMinus: 2,
      shots: 22,
      shootingPercentage: 4.5,
      powerPlayGoals: 0,
      shortHandedGoals: 0,
      gameWinningGoals: 0,
      timeOnIce: 21.5,
      powerPlayTime: 2.8,
      shortHandedTime: 4.2,
      penaltyMinutes: 18,
      penalties: 9,
      hits: 48,
      blockedShots: 35,
      faceoffWins: 0,
      faceoffLosses: 0,
      faceoffPercentage: 0,
    },
  },
  {
    id: "9",
    name: "Akira Yamamoto",
    position: "Goalie",
    jerseyNumber: 29,
    teamId: "tokyo-ice",
    avatar: "https://picsum.photos/120/120?random=9",
    description:
      "An athletic goaltender with quick reflexes and excellent rebound control. Known for his competitive spirit and ability to steal games with spectacular saves.",
    height: "6'2\"",
    weight: "185 lbs",
    birthDate: "2000-12-08",
    nationality: "Japan",
    statistics: {
      playerId: "9",
      tournamentId: "east-west-spring-classic",
      gamesPlayed: 4,
      goals: 0,
      assists: 0,
      points: 0,
      plusMinus: 0,
      shots: 0,
      shootingPercentage: 0,
      powerPlayGoals: 0,
      shortHandedGoals: 0,
      gameWinningGoals: 0,
      timeOnIce: 60.0,
      powerPlayTime: 0,
      shortHandedTime: 0,
      penaltyMinutes: 0,
      penalties: 0,
      hits: 0,
      blockedShots: 0,
      faceoffWins: 0,
      faceoffLosses: 0,
      faceoffPercentage: 0,
      // Goalie-specific stats
      saves: 118,
      shotsAgainst: 132,
      savePercentage: 89.4,
      goalsAgainst: 14,
      goalsAgainstAverage: 3.5,
      shutouts: 0,
      wins: 2,
      losses: 2,
      overtimeLosses: 0,
    },
  },
];

// Utility functions for retrieving player data
export const getPlayerById = (id: string): PlayerWithStatistics | undefined => {
  return mockPlayersWithStatistics.find((player) => player.id === id);
};

export const getPlayersByTeam = (teamId: string): PlayerWithStatistics[] => {
  return mockPlayersWithStatistics.filter((player) => player.teamId === teamId);
};

export const getTopScorers = (limit: number = 10): PlayerWithStatistics[] => {
  return mockPlayersWithStatistics
    .sort((a, b) => b.statistics.points - a.statistics.points)
    .slice(0, limit);
};

export const getTopGoalScorers = (
  limit: number = 10
): PlayerWithStatistics[] => {
  return mockPlayersWithStatistics
    .sort((a, b) => b.statistics.goals - a.statistics.goals)
    .slice(0, limit);
};

export const getGoalies = (): PlayerWithStatistics[] => {
  return mockPlayersWithStatistics.filter(
    (player) => player.position === "Goalie"
  );
};
