import { initializeApp, getApps, getApp } from 'firebase/app';
import { getFirestore, collection, getDocs, addDoc, doc, updateDoc, deleteDoc } from 'firebase/firestore';

// Initialize Firebase (you'll need to add your config)
const firebaseConfig = {
  // apiKey: "AIzaSyAXYi7MCm-aMBeh3bEjs0eJ5eHcGjf9-bw",
  // authDomain: "achieve-cb085.firebaseapp.com",
  // projectId: "achieve-cb085",
  // storageBucket: "achieve-cb085.firebasestorage.app",
  // messagingSenderId: "260668035138",
  // appId: "1:260668035138:web:ba67a28ffd83d01b279ddb",
  // measurementId: "G-JWFXTNTCGZ"
};

const app = getApps().length > 0 ? getApp() : initializeApp(firebaseConfig);
const db = getFirestore(app);

export interface Showcase {
  id: string;
  title: string;
  subtitle: string;
  date: string;
  tags: string[];
  image: string;
  country: string;
}

export interface ShowcaseDetails extends Showcase {
  location: string;
  participants: number;
  status: string;
  description: string;
}

// Cache for Showcases
let ShowcasesCache: ShowcaseDetails[] = [];

// Helper function to convert Firestore timestamp to date string
const convertFirestoreDate = (firestoreDate: any): string => {
  if (!firestoreDate) return 'Date TBD';
  
  // If it's a Firestore timestamp with seconds and nanoseconds
  if (firestoreDate.seconds && firestoreDate.nanoseconds !== undefined) {
    const date = new Date(firestoreDate.seconds * 1000);
    return date.toLocaleDateString();
  }
  
  // If it's already a Date object
  if (firestoreDate instanceof Date) {
    return firestoreDate.toLocaleDateString();
  }
  
  // If it's a string, return as is
  if (typeof firestoreDate === 'string') {
    return firestoreDate;
  }
  
  return 'Date TBD';
};

// Fetch Showcases from Firebase
export const fetchShowcases = async (): Promise<ShowcaseDetails[]> => {
  console.log('Fetching Showcases from Firebase...');
  
  try {
    const querySnapshot = await getDocs(collection(db, 'Showcases'));
    const Showcases: ShowcaseDetails[] = [];
    
    console.log('Firebase query snapshot size:', querySnapshot.size);
    
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      console.log('Showcase data:', data);
      
      // Use better fallback values for empty fields and handle Firestore timestamps
      const Showcase: ShowcaseDetails = {
        id: doc.id,
        title: data.title || data.name || `Showcase ${doc.id.slice(0, 8)}`,
        subtitle: data.subtitle || data.description || 'Showcase Details',
        date: convertFirestoreDate(data.date || data.startDate),
        tags: Array.isArray(data.tags) ? data.tags : (data.country ? [data.country] : []),
        image: data.image || 'https://picsum.photos/400/300?random=1',
        country: data.country || 'All',
        location: data.location || 'Location TBD',
        participants: data.participants || 0,
        status: data.status || 'Upcoming',
        description: data.description || 'Showcase description coming soon...',
      };
      
      Showcases.push(Showcase);
    });
    
    console.log('Processed Showcases:', Showcases.map(t => ({ id: t.id, title: t.title, subtitle: t.subtitle, date: t.date })));
    
    // Update cache with fetched data
    ShowcasesCache = Showcases;
    
    return Showcases;
  } catch (error) {
    console.error('Error fetching Showcases:', error);
    // Return default Showcases on error for better UX
    return getDefaultShowcases();
  }
};

// Add default Showcases as fallback
const getDefaultShowcases = (): ShowcaseDetails[] => {
  return [
    {
      id: "1",
      title: "East West Spring Classic",
      subtitle: "International Youth Showcase",
      date: "28 April – 1 May, 2025",
      tags: ["Hong Kong", "Korea", "U8–U18", "International"],
      image: "https://picsum.photos/400/300?random=1",
      country: "All",
      location: "Hong Kong Ice Hockey Academy",
      participants: 16,
      status: "Registration Open",
      description: "The East West Spring Classic is a premier international youth Showcase featuring teams from across Asia.",
    },
    {
      id: "2",
      title: "HKAHC",
      subtitle: "Invitational Amateur Ice Hockey Showcase",
      date: "15 Dec – 21 Feb, 2025",
      tags: ["Hong Kong", "Men's", "Local"],
      image: "https://picsum.photos/400/300?random=2",
      country: "China",
      location: "Mega Ice Arena",
      participants: 12,
      status: "Active",
      description: "The Hong Kong Amateur Hockey Club invitational Showcase brings together the best amateur teams from the region.",
    },
  ];
};

// Add Showcase to Firebase
export const addShowcase = async (Showcase: Omit<ShowcaseDetails, 'id'>): Promise<void> => {
  try {
    await addDoc(collection(db, 'Showcases'), Showcase);
    // Refresh cache
    await fetchShowcases();
  } catch (error) {
    console.error('Error adding Showcase:', error);
    throw error;
  }
};

// Update Showcase in Firebase
export const updateShowcase = async (id: string, updates: Partial<ShowcaseDetails>): Promise<void> => {
  try {
    const ShowcaseRef = doc(db, 'Showcases', id);
    await updateDoc(ShowcaseRef, updates);
    // Refresh cache
    await fetchShowcases();
  } catch (error) {
    console.error('Error updating Showcase:', error);
    throw error;
  }
};

// Delete Showcase from Firebase
export const deleteShowcase = async (id: string): Promise<void> => {
  try {
    await deleteDoc(doc(db, 'Showcases', id));
    // Refresh cache
    await fetchShowcases();
  } catch (error) {
    console.error('Error deleting Showcase:', error);
    throw error;
  }
};

// Get Showcases (always fetch from Firebase)
export const getShowcases = async (): Promise<ShowcaseDetails[]> => {
  return await fetchShowcases();
};

// Export Showcases for backward compatibility
export const Showcases = ShowcasesCache;

// Helper function to get Showcase by ID
export const getShowcaseById = (id: string): ShowcaseDetails | undefined => {
  return ShowcasesCache.find((Showcase) => Showcase.id === id);
};

export const countries = ["All", "China", "Korea", "Japan"];

export const getStatusColor = (status: string): string => {
  switch (status) {
    case "Active":
      return "#28a745";
    case "Registration Open":
      return "#007bff";
    case "Upcoming":
      return "#ffc107";
    case "Completed":
      return "#6c757d";
    default:
      return "#666";
  }
};
