import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, orderBy, where, getDoc } from 'firebase/firestore';
import { initializeApp, getApps, getApp } from 'firebase/app';

// Initialize Firebase (you'll need to add your config)
const firebaseConfig = {
  // apiKey: "AIzaSyAXYi7MCm-aMBeh3bEjs0eJ5eHcGjf9-bw",
  // authDomain: "achieve-cb085.firebaseapp.com",
  // projectId: "achieve-cb085",
  // storageBucket: "achieve-cb085.firebasestorage.app",
  // messagingSenderId: "260668035138",
  // appId: "1:260668035138:web:ba67a28ffd83d01b279ddb",
  // measurementId: "G-JWFXTNTCGZ"
};

const app = getApps().length > 0 ? getApp() : initializeApp(firebaseConfig);
const db = getFirestore(app);

export interface campsItem {
  docId: string; // Firestore document id
  title: string;
  subtitle: string;
  date: string;
  image: string;
  tags: string[];
}

export interface campsDetails extends campsItem {
  description?: string;
  location?: string;
  country?: string;
  participants?: number;
  status?: string;
}

export interface Camp {
  docId: string;
  title: string;
  subtitle: string;
  date: string;
  image: string;
  tags: string[];
  description?: string;
  location?: string;
  country?: string;
  participants?: number;
  status?: string;
  coach?: string;
  coachImage?: string;
  name?: string;
  startDate?: string;
  endDate?: string;
}

const COLLECTION_NAME = 'camps';

// Get all camps items from Firebase only (no circular dependencies)
export const getcampsItems = async (): Promise<campsItem[]> => {
  try {
    const firebaseItems: campsItem[] = [];
    const q = query(collection(db, COLLECTION_NAME), orderBy('date', 'desc'));
    const querySnapshot = await getDocs(q);
    querySnapshot.forEach((docSnap) => {
      const data = docSnap.data();
      console.log('Fetched camps item:', docSnap.id, data)
      firebaseItems.push({
        docId: docSnap.id,
        title: data.title,
        subtitle: data.subtitle,
        date: data.date,
        image: data.image,
        tags: data.tags || [],
      });
    });
    return firebaseItems.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  } catch (error) {
    console.error('Error fetching camps items:', error);
    return [];
  }
};

// Generate a unique random ID for the camps item
async function generateUniqueRandomId(length = 16): Promise<string> {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let id: string;
  let exists = true;
  while (exists) {
    id = Array.from({ length }, () => chars.charAt(Math.floor(Math.random() * chars.length))).join('');
    const q = query(collection(db, COLLECTION_NAME), where("id", "==", id));
    const snapshot = await getDocs(q);
    exists = !snapshot.empty;
  }
  return id!;
}

// Add new camps item with unique random id
export const addcampsItem = async (itemData: Omit<campsDetails, 'docId'>): Promise<string> => {
  try {
    const docRef = await addDoc(collection(db, COLLECTION_NAME), {
      ...itemData,
      createdAt: new Date().toISOString(),
    });
    return docRef.id;
  } catch (error) {
    console.error('Error adding camps item:', error);
    throw new Error('Failed to add camps item');
  }
};

// Update camps item
export const updatecampsItem = async (docId: string, itemData: Partial<campsDetails>): Promise<void> => {
  try {
    const docRef = doc(db, COLLECTION_NAME, docId);
    await updateDoc(docRef, {
      ...itemData,
      updatedAt: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error updating camps item:', error);
    throw new Error('Failed to update camps item');
  }
};

// Delete camps item
export const deletecampsItem = async (docId: string): Promise<void> => {
  try {
    const docRef = doc(db, COLLECTION_NAME, docId);
    await deleteDoc(docRef);
  } catch (error) {
    console.error('Error deleting camps item:', error);
    throw new Error('Failed to delete camps item');
  }
};

// Get camp by ID
export const getCampById = async (docId: string): Promise<Camp | undefined> => {
  try {
    const docRef = doc(db, COLLECTION_NAME, docId);
    const docSnap = await getDoc(docRef);
    if (docSnap.exists()) {
      const data = docSnap.data();
      return {
        docId,
        title: data.title,
        subtitle: data.subtitle,
        date: data.date,
        image: data.image,
        tags: data.tags || [],
        description: data.description,
        location: data.location,
        country: data.country,
        participants: data.participants,
        status: data.status,
        coach: data.coach,
        coachImage: data.coachImage,
        name: data.name || data.title,
        startDate: data.startDate || data.date,
        endDate: data.endDate || data.date,
      };
    }
    return undefined;
  } catch (error) {
    console.error('Error fetching camp by id:', error);
    return undefined;
  }
};

// Legacy export for backward compatibility
export const campsData: campsItem[] = [];
