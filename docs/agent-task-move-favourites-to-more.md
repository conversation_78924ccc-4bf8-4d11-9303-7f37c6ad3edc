# Agent Task: Remove Favourites Tab and Move to More Tab as a Container

## Overview
This task involves removing the standalone "Favourites" tab from the main navigation and integrating its functionality as a container within the "More" tab. The goal is to maintain the current design, styling, and layout of the favourites feature, ensuring a seamless user experience and no loss of functionality. The header for the favourites screen can be refactored or removed as appropriate for its new context.

## Goals
- Remove the "Favourites" tab from the main tab navigation.
- Move the favourites screen and its components to be accessible from the "More" tab, using the existing placeholder.
- Preserve the current design, styling, and layout of the favourites feature.
- Refactor or remove the custom favourites header as needed.
- Ensure no bugs or regressions are introduced.

## Tasks (Step by Step)
1. **Remove Favourites Tab**
   - Edit `app/(tabs)/_layout.tsx` to remove the `favourites` tab from the `<Tabs>` navigation.
2. **Move Favourites Screen**
   - Move the contents of `app/(tabs)/favourites/index.tsx` and its components to a new container component under the `More` tab (e.g., `app/(tabs)/more/FavouritesContainer.tsx`).
   - Ensure all imports and relative paths are updated accordingly.
3. **Update More Tab Navigation**
   - In `app/(tabs)/more/index.tsx`, update the placeholder for "Favourite teams" to navigate to the new container component.
   - Ensure navigation works as expected.
4. **Refactor Header**
   - Refactor or remove the custom `Header` component from the favourites screen as appropriate for its new context within the More tab.
   - Ensure the design and layout remain consistent with the rest of the app.
5. **Update Team Details Navigation**
   - Update any navigation logic for team details to work from the new location.
   - Ensure deep linking and back navigation function correctly.
6. **Clean Up**
   - Remove any unused files, imports, or references related to the old favourites tab.
   - Update documentation and type definitions if necessary.
7. **Test Thoroughly**
   - Test the new implementation on all platforms (iOS, Android, Web).
   - Verify that the design, layout, and functionality of the favourites feature are preserved.
   - Ensure there are no navigation or rendering bugs.

## Acceptance Criteria
- The "Favourites" tab is no longer present in the main tab navigation.
- The favourites feature is accessible from the "More" tab, using the existing placeholder.
- The design, styling, and layout of the favourites feature are unchanged.
- The custom header is refactored or removed as appropriate.
- Navigation to team details from favourites works as before.
- No bugs or regressions are introduced.
- All relevant files and imports are updated and cleaned up.

## Relevant Files
- `app/(tabs)/_layout.tsx`
- `app/(tabs)/favourites/index.tsx`
- `app/(tabs)/favourites/components/`
- `app/(tabs)/favourites/team-details.tsx`
- `app/(tabs)/more/index.tsx`
- `app/(tabs)/more/` (new container component location)
- `types/index.ts`
- `README.md`
- `docs/tech-backlog.md`

## Guidelines
- **Preserve Design:** Follow the existing design, styling, and layout as implemented in the current project. Do not introduce new UI changes unless necessary for integration.
- **Navigation:** Ensure navigation flows are intuitive and consistent with the rest of the app. Use Expo Router best practices.
- **Component Reuse:** Reuse existing components where possible. Refactor only as needed for the new context.
- **Code Quality:** Maintain clean, readable, and well-organized code. Remove unused code and files.
- **Testing:** Test thoroughly on all supported platforms. Check for navigation, rendering, and functional bugs.
- **Documentation:** Update documentation and type definitions if any changes are made to data structures or navigation.
- **No Bugs:** Ensure the final implementation is bug-free and meets all acceptance criteria.
